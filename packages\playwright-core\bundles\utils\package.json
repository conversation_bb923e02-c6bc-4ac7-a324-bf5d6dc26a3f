{"name": "utils-bundle", "version": "0.0.1", "private": true, "dependencies": {"colors": "1.4.0", "commander": "^13.0.0", "debug": "^4.3.4", "diff": "^7.0.0", "dotenv": "^16.4.5", "graceful-fs": "4.2.10", "https-proxy-agent": "5.0.1", "jpeg-js": "0.4.4", "mime": "^3.0.0", "minimatch": "^3.1.2", "open": "8.4.0", "pngjs": "6.0.0", "progress": "2.0.3", "proxy-from-env": "1.1.0", "retry": "0.12.0", "signal-exit": "3.0.7", "socks-proxy-agent": "6.1.1", "ws": "8.17.1", "yaml": "^2.6.0"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/diff": "^6.0.0", "@types/mime": "^2.0.3", "@types/minimatch": "^3.0.5", "@types/pngjs": "^6.0.1", "@types/progress": "^2.0.5", "@types/proper-lockfile": "^4.1.2", "@types/proxy-from-env": "^1.0.1", "@types/stack-utils": "^2.0.1", "@types/ws": "8.2.2"}}