/**
 * 测试代码生成拦截修复
 */

// 设置环境变量
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = 'true';

async function testCodeGenerationFix() {
  console.log('🧪 测试代码生成拦截修复...');
  
  try {
    // 1. 加载playwright-core触发patch
    console.log('📦 加载playwright-core...');
    require('playwright-core');
    
    // 等待patch应用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 检查全局bridge是否可用
    if (global.playwrightElectronBridge && global.playwrightElectronBridge.originalSetSources) {
      console.log('✅ playwrightElectronBridge.originalSetSources 可用');
      
      // 3. 模拟代码生成调用
      console.log('🧪 模拟代码生成调用...');
      
      const mockSources = [
        {
          isPrimary: true,
          timestamp: Date.now(),
          isRecorded: true,
          label: 'JavaScript',
          id: 'javascript',
          text: 'const { chromium } = require("playwright");\n\n(async () => {\n  const browser = await chromium.launch();\n  const page = await browser.newPage();\n  await page.goto("https://example.com");\n  await page.click("#button");\n  await browser.close();\n})();',
          language: 'javascript',
          highlight: []
        }
      ];
      
      const mockPrimaryPageURL = 'https://example.com';
      
      // 设置一个标志来检测是否被调用
      let interceptCalled = false;
      const originalFunction = global.playwrightElectronBridge.originalSetSources;
      
      global.playwrightElectronBridge.originalSetSources = function(sources, primaryPageURL) {
        interceptCalled = true;
        console.log('🎯 拦截函数被调用!');
        console.log('  sources数量:', sources.length);
        console.log('  primaryPageURL:', primaryPageURL);
        console.log('  第一个source的text长度:', sources[0]?.text?.length || 0);
        
        // 调用原始函数
        return originalFunction.call(this, sources, primaryPageURL);
      };
      
      // 模拟调用
      global.playwrightElectronBridge.originalSetSources(mockSources, mockPrimaryPageURL);
      
      if (interceptCalled) {
        console.log('✅ 代码生成拦截功能正常工作');
      } else {
        console.log('❌ 代码生成拦截功能未被调用');
      }
      
    } else {
      console.log('❌ playwrightElectronBridge.originalSetSources 不可用');
    }
    
    // 4. 检查recorderApp.js是否已被修改
    console.log('📋 检查recorderApp.js修改状态...');
    
    const fs = require('fs');
    const path = require('path');
    const recorderAppPath = path.join('node_modules', 'playwright-core', 'lib', 'server', 'recorder', 'recorderApp.js');
    
    if (fs.existsSync(recorderAppPath)) {
      const content = fs.readFileSync(recorderAppPath, 'utf-8');
      const hasIntercept = content.includes('ELECTRON_BRIDGE_INTERCEPT');
      const hasOriginalSetSources = content.includes('originalSetSources');
      
      console.log('  包含拦截代码:', hasIntercept ? '✅' : '❌');
      console.log('  包含originalSetSources调用:', hasOriginalSetSources ? '✅' : '❌');
      
      if (hasIntercept && hasOriginalSetSources) {
        console.log('✅ recorderApp.js已正确修改');
      } else {
        console.log('❌ recorderApp.js修改不完整');
      }
    } else {
      console.log('❌ recorderApp.js文件不存在');
    }
    
    // 5. 测试消息处理器
    console.log('📨 测试消息处理器...');

    try {
      const MessageHandler = require('./src/main/message-handler');

      // 创建一个模拟的recorder对象
      const mockRecorder = {
        sendToRenderer: (event, data) => {
          console.log('📤 发送到渲染进程:', event, {
            sourcesCount: data.sources?.length || 0,
            primaryPageURL: data.primaryPageURL
          });
        },
        generatedCode: 'console.log("test");'
      };

      const messageHandler = new MessageHandler(mockRecorder);

      // 测试代码生成处理
      const testData = {
        type: 'playwrightCodeGenerated',
        sources: mockSources,
        primaryPageURL: mockPrimaryPageURL,
        timestamp: Date.now()
      };

      messageHandler._handlePlaywrightCodeGenerated(testData);
      console.log('✅ 消息处理器工作正常');
    } catch (error) {
      console.log('⚠️ 消息处理器测试跳过:', error.message);
    }
    
    console.log('\n🎉 代码生成拦截修复测试完成！');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testCodeGenerationFix().then(success => {
    if (success) {
      console.log('\n✨ 代码生成拦截修复成功！');
      console.log('📋 现在当Playwright生成代码时，应该能够在录制器工具栏中看到代码了');
    } else {
      console.log('\n💥 修复测试失败');
    }
  }).catch(console.error);
}

module.exports = { testCodeGenerationFix };
