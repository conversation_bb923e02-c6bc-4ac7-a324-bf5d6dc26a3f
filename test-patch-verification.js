/**
 * 验证patch是否正确应用的测试脚本
 */

// 设置环境变量以激活patch
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = 'true';

async function verifyPatchApplication() {
  console.log('🔍 验证patch应用状态...');
  
  // 检查环境变量
  console.log('📋 环境变量检查:');
  console.log('  PLAYWRIGHT_ELECTRON_BRIDGE:', process.env.PLAYWRIGHT_ELECTRON_BRIDGE);
  
  try {
    // 尝试加载playwright-core模块来触发patch
    console.log('📦 加载playwright-core模块...');
    const playwright = require('playwright-core');
    
    // 等待一下让patch有时间应用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 检查全局API是否可用
    console.log('🔍 检查全局API可用性:');
    
    if (global.playwrightReplayAPI) {
      console.log('✅ global.playwrightReplayAPI 可用');
      console.log('📊 API详情:', {
        version: global.playwrightReplayAPI.version,
        isPatchedAPI: global.playwrightReplayAPI.isPatchedAPI,
        patchVersion: global.playwrightReplayAPI.patchVersion,
        hasPerformAction: typeof global.playwrightReplayAPI.performAction === 'function',
        hasCreateActionInContext: typeof global.playwrightReplayAPI.createActionInContext === 'function',
        hasExecuteActionSequence: typeof global.playwrightReplayAPI.executeActionSequence === 'function'
      });
    } else {
      console.log('❌ global.playwrightReplayAPI 不可用');
    }
    
    if (global.playwrightElectronBridge) {
      console.log('✅ global.playwrightElectronBridge 可用');
      console.log('📊 Bridge详情:', {
        hasOriginalSetSources: typeof global.playwrightElectronBridge.originalSetSources === 'function'
      });
    } else {
      console.log('❌ global.playwrightElectronBridge 不可用');
    }
    
    // 测试API功能
    if (global.playwrightReplayAPI && global.playwrightReplayAPI.createActionInContext) {
      console.log('🧪 测试API功能...');
      
      const testAction = {
        name: 'click',
        selector: '#test-button'
      };
      
      const actionInContext = global.playwrightReplayAPI.createActionInContext('main', [], testAction);
      
      console.log('✅ createActionInContext 测试成功:', {
        framePageAlias: actionInContext.frame.pageAlias,
        actionName: actionInContext.action.name,
        actionSelector: actionInContext.action.selector,
        hasStartTime: !!actionInContext.startTime
      });
    }
    
    // 检查patch文件是否存在
    const fs = require('fs');
    const path = require('path');
    const patchPath = path.join(__dirname, 'patches', 'playwright-core*****.1.patch');
    
    if (fs.existsSync(patchPath)) {
      console.log('✅ patch文件存在:', patchPath);
      
      // 读取patch文件内容检查关键部分
      const patchContent = fs.readFileSync(patchPath, 'utf-8');
      
      const hasElectronBridge = patchContent.includes('ELECTRON_BRIDGE_PATCH_APPLIED');
      const hasGlobalReplayAPI = patchContent.includes('GLOBAL_REPLAY_API_PATCH');
      const hasGlobalUtilsAPI = patchContent.includes('GLOBAL_UTILS_API_PATCH');
      
      console.log('📋 patch内容检查:', {
        hasElectronBridge,
        hasGlobalReplayAPI,
        hasGlobalUtilsAPI
      });
      
      if (hasElectronBridge && hasGlobalReplayAPI && hasGlobalUtilsAPI) {
        console.log('✅ patch文件内容完整');
      } else {
        console.log('⚠️ patch文件内容可能不完整');
      }
    } else {
      console.log('❌ patch文件不存在:', patchPath);
    }
    
    // 检查node_modules中的文件是否已被patch
    const recorderPath = path.join(__dirname, 'node_modules', 'playwright-core', 'lib', 'server', 'recorder.js');
    const recorderRunnerPath = path.join(__dirname, 'node_modules', 'playwright-core', 'lib', 'server', 'recorder', 'recorderRunner.js');
    const recorderUtilsPath = path.join(__dirname, 'node_modules', 'playwright-core', 'lib', 'server', 'recorder', 'recorderUtils.js');
    
    console.log('📋 检查已patch的文件:');
    
    if (fs.existsSync(recorderPath)) {
      const recorderContent = fs.readFileSync(recorderPath, 'utf-8');
      const hasElectronPatch = recorderContent.includes('ELECTRON_BRIDGE_PATCH_APPLIED');
      console.log('  recorder.js:', hasElectronPatch ? '✅ 已patch' : '❌ 未patch');
    }
    
    if (fs.existsSync(recorderRunnerPath)) {
      const runnerContent = fs.readFileSync(recorderRunnerPath, 'utf-8');
      const hasReplayPatch = runnerContent.includes('GLOBAL_REPLAY_API_PATCH');
      console.log('  recorderRunner.js:', hasReplayPatch ? '✅ 已patch' : '❌ 未patch');
    }
    
    if (fs.existsSync(recorderUtilsPath)) {
      const utilsContent = fs.readFileSync(recorderUtilsPath, 'utf-8');
      const hasUtilsPatch = utilsContent.includes('GLOBAL_UTILS_API_PATCH');
      console.log('  recorderUtils.js:', hasUtilsPatch ? '✅ 已patch' : '❌ 未patch');
    }
    
    console.log('\n🎉 patch验证完成！');
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  }
}

// 运行验证
if (require.main === module) {
  verifyPatchApplication().catch(console.error);
}

module.exports = { verifyPatchApplication };
