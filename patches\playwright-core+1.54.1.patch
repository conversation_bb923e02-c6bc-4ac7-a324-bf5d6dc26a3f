diff --git a/node_modules/playwright-core/lib/server/recorder.js b/node_modules/playwright-core/lib/server/recorder.js
index 6646e29..b0a29a5 100644
--- a/node_modules/playwright-core/lib/server/recorder.js
+++ b/node_modules/playwright-core/lib/server/recorder.js
@@ -31,6 +31,53 @@ __export(recorder_exports, {
   Recorder: () => Recorder,
   RecorderEvent: () => RecorderEvent
 });
+
+// ELECTRON_BRIDGE_PATCH_APPLIED - Electron 集成补丁
+if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+  try {
+    // 监听录制器事件并发送到 Electron
+    const originalSetSources = function(sources, primaryPageURL) {
+      // 直接通过 Electron IPC 发送数据到主进程
+      const electronData = {
+        type: 'playwrightCodeGenerated',
+        sources: sources,
+        primaryPageURL: primaryPageURL,
+        timestamp: Date.now()
+      };
+
+      // 检查是否在 Electron 环境中
+      if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+        // 直接使用 Electron 的 IPC 通信
+        const { BrowserWindow } = require('electron');
+        const allWindows = BrowserWindow.getAllWindows();
+
+        // 发送到所有渲染进程
+        allWindows.forEach(window => {
+          if (!window.isDestroyed()) {
+            window.webContents.send('playwright-code-generated', electronData);
+          }
+        });
+
+        // 尝试直接调用全局的 Electron 录制器实例（如果存在）
+        if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+          global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+          console.log('📡 已直接调用 Electron 录制器处理 Playwright 代码生成');
+        } else {
+          console.log('📡 已通过 Electron IPC 发送代码生成数据到所有窗口');
+        }
+      } else {
+        console.log('⚠️ 不在 Electron 环境中，跳过 IPC 通信');
+      }
+    };
+    
+    // 导出函数供外部使用
+    global.playwrightElectronBridge = { originalSetSources };
+    console.log('🌉 Electron 集成补丁已应用');
+  } catch (error) {
+    console.log('⚠️ Electron IPC 集成失败:', error.message);
+  }
+}
+
 module.exports = __toCommonJS(recorder_exports);
 var import_events = __toESM(require("events"));
 var import_fs = __toESM(require("fs"));
diff --git a/node_modules/playwright-core/lib/server/recorder/recorderApp.js b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
index 547a51a..56a0fe2 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderApp.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderApp.js
@@ -288,6 +288,68 @@ class RecorderApp {
   }
   async _pushAllSources() {
     const sources = [...this._userSources, ...this._recorderSources];
+
+    // ELECTRON_BRIDGE_INTERCEPT - 拦截代码生成并发送到Electron
+    if (process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
+      try {
+        const primaryPageURL = this._page?.mainFrame()?.url();
+        const electronData = {
+          type: 'playwrightCodeGenerated',
+          sources: sources,
+          primaryPageURL: primaryPageURL,
+          timestamp: Date.now()
+        };
+
+        console.log('📡 已拦截代码生成数据，sources数量:', sources.length);
+
+        // 方法1: 直接调用全局的 Electron 录制器实例（如果在同一进程中）
+        if (global.electronPlaywrightRecorder && global.electronPlaywrightRecorder.messageHandler) {
+          global.electronPlaywrightRecorder.messageHandler._handlePlaywrightCodeGenerated(electronData);
+          console.log('📡 通过全局对象发送成功');
+        }
+        // 方法2: 通过页面注入的方式发送到主进程
+        else if (this._page) {
+          this._page.mainFrame().evaluateExpression(((data) => {
+            // 在页面中发送消息到主进程
+            if (typeof sendToElectron === 'function') {
+              sendToElectron(data);
+              console.log('📡 通过sendToElectron发送成功');
+            } else if (window.electronAPI && window.electronAPI.sendToMain) {
+              window.electronAPI.sendToMain('playwright-code-generated', data);
+              console.log('📡 通过electronAPI发送成功');
+            } else if (window.ipcRenderer) {
+              window.ipcRenderer.send('playwright-code-generated', data);
+              console.log('📡 通过ipcRenderer发送成功');
+            } else {
+              console.log('⚠️ 页面中无可用的IPC通信方式');
+            }
+          }).toString(), { isFunction: true }, electronData).catch((error) => {
+            console.log('⚠️ 页面注入发送失败:', error.message);
+          });
+          console.log('📡 通过页面注入发送');
+        }
+        // 方法3: 尝试通过Electron的BrowserWindow发送（如果在Electron环境中）
+        else if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
+          try {
+            const { BrowserWindow } = require('electron');
+            const allWindows = BrowserWindow.getAllWindows();
+            allWindows.forEach(window => {
+              if (!window.isDestroyed()) {
+                window.webContents.send('playwright-code-generated', electronData);
+              }
+            });
+            console.log('📡 通过BrowserWindow发送到所有窗口');
+          } catch (error) {
+            console.log('⚠️ BrowserWindow发送失败:', error.message);
+          }
+        } else {
+          console.log('⚠️ 无可用的发送方式');
+        }
+      } catch (error) {
+        console.log('⚠️ 代码生成拦截失败:', error.message);
+      }
+    }
+
     this._page.mainFrame().evaluateExpression((({ sources: sources2 }) => {
       window.playwrightSetSources(sources2);
     }).toString(), { isFunction: true }, { sources }).catch(() => {
diff --git a/node_modules/playwright-core/lib/server/recorder/recorderRunner.js b/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
index e679363..ea20526 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderRunner.js
@@ -134,3 +134,94 @@ function toClickOptions(action) {
   performAction,
   toClickOptions
 });
+
+
+// GLOBAL_REPLAY_API_PATCH - 将回放接口暴露到全局
+if (typeof global !== 'undefined') {
+  // 确保全局 API 对象存在，如果已存在则合并
+  if (!global.playwrightReplayAPI) {
+    global.playwrightReplayAPI = {};
+  }
+
+  // 创建官方兼容的包装器函数
+  const wrappedPerformAction = async function(pageAliases, actionInContext) {
+    try {
+      // 首先尝试使用官方的 performAction
+      return await performAction(pageAliases, actionInContext);
+    } catch (error) {
+      // 如果出现 callMetadata 相关错误，使用备用方案
+      if (error.message && error.message.includes('expected string, got object')) {
+        console.log('🔄 检测到 callMetadata 兼容性问题，使用备用方案...');
+
+        // 备用方案：直接调用底层 API，不使用 callMetadata
+        const mainFrame = (0, import_recorderUtils.mainFrameForAction)(pageAliases, actionInContext);
+        const { action } = actionInContext;
+        const kActionTimeout = 5000;
+
+        if (action.name === "navigate") {
+          await mainFrame.goto(action.url, { timeout: kActionTimeout });
+          return;
+        }
+
+        if (action.name === "closePage") {
+          await mainFrame._page.close();
+          return;
+        }
+
+        const selector = (0, import_recorderUtils.buildFullSelector)(actionInContext.frame.framePath, action.selector);
+
+        if (action.name === "click") {
+          const options = toClickOptions(action);
+          await mainFrame.click(selector, { ...options, timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "fill") {
+          await mainFrame.fill(selector, action.text, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "press") {
+          const modifiers = (0, import_language.toKeyboardModifiers)(action.modifiers);
+          const shortcut = [...modifiers, action.key].join("+");
+          await mainFrame.press(selector, shortcut, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "check") {
+          await mainFrame.check(selector, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "uncheck") {
+          await mainFrame.uncheck(selector, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        if (action.name === "select") {
+          const values = action.options.map((value) => ({ value }));
+          await mainFrame.selectOption(selector, [], values, { timeout: kActionTimeout, strict: true });
+          return;
+        }
+
+        throw new Error("Internal error: unexpected action " + action.name);
+      }
+
+      // 重新抛出其他错误
+      throw error;
+    }
+  };
+
+  // 合并核心回放函数到全局对象（不覆盖已有的函数）
+  Object.assign(global.playwrightReplayAPI, {
+    performAction: wrappedPerformAction,  // 使用包装版本
+    performActionOriginal: performAction,  // 保留原版供参考
+    toClickOptions: toClickOptions,
+    // 版本信息
+    version: '1.54.1',
+    // 标记这是通过patch暴露的API
+    isPatchedAPI: true,
+    patchVersion: '1.0.0'
+  });
+  console.log('🌍 Playwright 回放 API 已暴露到 global.playwrightReplayAPI');
+}
diff --git a/node_modules/playwright-core/lib/server/recorder/recorderUtils.js b/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
index 67b59e6..5d65cfb 100644
--- a/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
+++ b/node_modules/playwright-core/lib/server/recorder/recorderUtils.js
@@ -144,3 +144,53 @@ async function generateFrameSelectorInParent(parent, frame) {
   metadataToCallLog,
   shouldMergeAction
 });
+
+
+// GLOBAL_UTILS_API_PATCH - 将工具函数暴露到全局
+if (typeof global !== 'undefined') {
+  // 确保全局 API 对象存在
+  if (!global.playwrightReplayAPI) {
+    global.playwrightReplayAPI = {};
+  }
+
+  // 扩展全局 API，添加更多工具函数
+  Object.assign(global.playwrightReplayAPI, {
+    // 工具函数
+    buildFullSelector: buildFullSelector,
+    mainFrameForAction: mainFrameForAction,
+    frameForAction: frameForAction,
+
+    // 创建标准的 ActionInContext 对象的辅助函数
+    createActionInContext: function(pageAlias, framePath, action) {
+      return {
+        frame: {
+          pageAlias: pageAlias || 'page',
+          framePath: framePath || []
+        },
+        action: {
+          signals: [],
+          ...action
+        },
+        startTime: Date.now()
+      };
+    },
+
+    // 批量执行动作的辅助函数
+    executeActionSequence: async function(pageAliases, actions) {
+      const results = [];
+      for (const actionInContext of actions) {
+        try {
+          await global.playwrightReplayAPI.performAction(pageAliases, actionInContext);
+          actionInContext.endTime = Date.now();
+          results.push({ success: true, action: actionInContext });
+        } catch (error) {
+          results.push({ success: false, action: actionInContext, error: error.message });
+          throw error; // 重新抛出错误以保持原有行为
+        }
+      }
+      return results;
+    }
+  });
+
+  console.log('🔧 Playwright 工具函数已添加到 global.playwrightReplayAPI');
+}
