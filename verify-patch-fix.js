/**
 * 验证patch修复的简单脚本
 */

// 设置环境变量
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = 'true';

async function verifyPatchFix() {
  console.log('🔧 验证patch修复状态...');
  
  try {
    // 1. 检查patch文件版本匹配
    const fs = require('fs');
    const path = require('path');
    
    // 检查package.json中的版本
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf-8'));
    const playwrightCoreVersion = packageJson.dependencies['playwright-core'];
    console.log('📦 package.json中的playwright-core版本:', playwrightCoreVersion);
    
    // 检查实际安装的版本
    const nodeModulesPath = path.join('node_modules', 'playwright-core', 'package.json');
    if (fs.existsSync(nodeModulesPath)) {
      const installedPackage = JSON.parse(fs.readFileSync(nodeModulesPath, 'utf-8'));
      console.log('📦 实际安装的playwright-core版本:', installedPackage.version);
      
      // 检查patch文件名是否匹配
      const expectedPatchFile = `playwright-core+${installedPackage.version}.patch`;
      const patchPath = path.join('patches', expectedPatchFile);
      
      if (fs.existsSync(patchPath)) {
        console.log('✅ patch文件版本匹配:', expectedPatchFile);
      } else {
        console.log('❌ patch文件版本不匹配，期望:', expectedPatchFile);
        
        // 列出现有的patch文件
        const patchesDir = path.join('patches');
        if (fs.existsSync(patchesDir)) {
          const patchFiles = fs.readdirSync(patchesDir).filter(f => f.endsWith('.patch'));
          console.log('📋 现有patch文件:', patchFiles);
        }
      }
    }
    
    // 2. 加载playwright-core并检查patch是否应用
    console.log('📦 加载playwright-core...');
    require('playwright-core');
    
    // 等待patch应用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 检查全局API
    console.log('🔍 检查全局API状态:');
    
    if (global.playwrightReplayAPI) {
      console.log('✅ global.playwrightReplayAPI 可用');
      console.log('📊 API信息:', {
        version: global.playwrightReplayAPI.version,
        isPatchedAPI: global.playwrightReplayAPI.isPatchedAPI,
        patchVersion: global.playwrightReplayAPI.patchVersion
      });
      
      // 测试关键功能
      if (typeof global.playwrightReplayAPI.performAction === 'function') {
        console.log('✅ performAction 函数可用');
      } else {
        console.log('❌ performAction 函数不可用');
      }
      
      if (typeof global.playwrightReplayAPI.createActionInContext === 'function') {
        console.log('✅ createActionInContext 函数可用');
      } else {
        console.log('❌ createActionInContext 函数不可用');
      }
      
    } else {
      console.log('❌ global.playwrightReplayAPI 不可用');
      console.log('🔍 可能的原因:');
      console.log('  1. patch文件版本不匹配');
      console.log('  2. patch未正确应用');
      console.log('  3. 环境变量未设置');
    }
    
    // 4. 测试我们的执行器
    console.log('🎬 测试执行器集成:');
    
    const { ScriptReplayExecutor } = require('./src/main/script-replay-executor');
    const { ReliableJsonReplayExecutor } = require('./src/main/reliable-json-replay-executor');
    
    const scriptExecutor = new ScriptReplayExecutor();
    const jsonExecutor = new ReliableJsonReplayExecutor();
    
    // 测试转换方法是否存在
    if (typeof scriptExecutor._convertToActionInContext === 'function') {
      console.log('✅ ScriptReplayExecutor._convertToActionInContext 可用');
    } else {
      console.log('❌ ScriptReplayExecutor._convertToActionInContext 不可用');
    }
    
    if (typeof jsonExecutor._convertToActionInContext === 'function') {
      console.log('✅ ReliableJsonReplayExecutor._convertToActionInContext 可用');
    } else {
      console.log('❌ ReliableJsonReplayExecutor._convertToActionInContext 不可用');
    }
    
    // 测试转换功能
    const testAction = {
      name: 'click',
      selector: '#test',
      pageAlias: 'main'
    };
    
    try {
      const converted = scriptExecutor._convertToActionInContext(testAction);
      console.log('✅ 动作转换测试成功');
    } catch (error) {
      console.log('❌ 动作转换测试失败:', error.message);
    }
    
    // 5. 检查patch应用状态
    console.log('📋 检查文件patch状态:');
    
    const filesToCheck = [
      'node_modules/playwright-core/lib/server/recorder.js',
      'node_modules/playwright-core/lib/server/recorder/recorderRunner.js',
      'node_modules/playwright-core/lib/server/recorder/recorderUtils.js'
    ];
    
    for (const filePath of filesToCheck) {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');
        const hasPatches = content.includes('PATCH') || content.includes('global.playwrightReplayAPI');
        console.log(`  ${path.basename(filePath)}: ${hasPatches ? '✅ 已patch' : '❌ 未patch'}`);
      } else {
        console.log(`  ${path.basename(filePath)}: ❌ 文件不存在`);
      }
    }
    
    console.log('\n🎉 patch修复验证完成！');
    
    // 总结
    const isWorking = global.playwrightReplayAPI && 
                     typeof global.playwrightReplayAPI.performAction === 'function' &&
                     typeof scriptExecutor._convertToActionInContext === 'function';
    
    if (isWorking) {
      console.log('✅ patch集成工作正常，回放功能已准备就绪！');
    } else {
      console.log('⚠️ patch集成可能存在问题，需要进一步检查');
    }
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  }
}

// 运行验证
if (require.main === module) {
  verifyPatchFix().catch(console.error);
}

module.exports = { verifyPatchFix };
