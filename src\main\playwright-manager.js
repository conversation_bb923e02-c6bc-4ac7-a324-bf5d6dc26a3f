/**
 * Playwright 管理器 - 管理 Playwright 浏览器和录制器
 */

const { chromium } = require('playwright');
const path = require('path');
const { APP_CONFIG, SUCCESS_MESSAGES } = require('./constants');
const NetworkMonitor = require('./network-monitor');
const ContextCommunicationManager = require('./context-communication-manager');

class PlaywrightManager {
  constructor(recorder) {
    this.recorder = recorder;
    this.networkMonitor = new NetworkMonitor(recorder);

    // 使用 context 级别的通信管理器
    this.contextCommunicationManager = null;

    // 🎯 录制器状态管理
    this._lastActionId = null;  // 用于去重
    this._actionSequence = 0;   // 动作序列号
    this._contextInitialized = false;  // 上下文是否已初始化
  }

  /**
   * 初始化 Playwright 录制器
   */
  async initialize() {
    await this._launchBrowser();
    await this._createBrowserContext();
    await this._enableOfficialRecorder();
    await this._createPage();
    await this._setupContextCommunication();
    await this._setupNetworkMonitoring();
    
    this.recorder.isRecording = true;
    console.log(SUCCESS_MESSAGES.PLAYWRIGHT_INITIALIZED);
  }

  /**
   * 启动浏览器
   * @private
   */
  async _launchBrowser() {
    const args = [
      `--app-name=${this.recorder.options.windowTitle}`,
      ...APP_CONFIG.BROWSER_ARGS
    ];

    this.recorder.browser = await chromium.launch({
      headless: false,
      args
    });
  }

  /**
   * 创建浏览器上下文
   * @private
   */
  async _createBrowserContext() {
    this.recorder.context = await this.recorder.browser.newContext({
      viewport: null,
      /* recordVideo: {
        dir: path.join(__dirname, '../../recordings/'),
        size: { width: 1280, height: 720 }
      } */
    });
  }

  /**
   * 启用官方录制器
   * @private
   */
  async _enableOfficialRecorder() {
    console.log('🎭 开始启用官方录制器...');
    console.log('📋 录制器配置:', {
      language: 'jsonl',  // 使用JSONL语言生成器
      mode: 'recording',
      outputFile: this.recorder.options.outputFile,
      recorderMode: 'api'  // 使用API模式
    });

    try {
      // 🎯 使用API模式，避免UI界面，并监听事件生成JSON代码
      await this.recorder.context._enableRecorder({
        language: 'jsonl',  // 使用JSONL语言生成器
        mode: 'recording',
        outputFile: this.recorder.options.outputFile,
        handleSIGINT: false,
        launchOptions: { headless: false },
        contextOptions: {},
        recorderMode: 'api'  // 使用API模式
      }, {
        // 🎯 监听录制器事件并生成JSON代码
        actionAdded: (_page, actionInContext) => {
          this._handleRecorderAction('actionAdded', actionInContext);
        },
        actionUpdated: (_page, actionInContext) => {
          this._handleRecorderAction('actionUpdated', actionInContext);
        },
        signalAdded: (_page, signalInContext) => {
          this._handleRecorderSignal('signalAdded', signalInContext);
        }
      });

      console.log('✅ 官方录制器已启用 (API模式)');

      // 🎯 生成初始化JSON数据（模拟1.53.1版本的行为）
      await this._generateInitialJsonData();
    } catch (error) {
      console.error('❌ 启用官方录制器失败:', error);
      throw error;
    }
  }

  /**
   * 处理录制器动作事件
   * @param {string} eventType - 事件类型
   * @param {Object} actionInContext - 动作上下文
   * @private
   */
  _handleRecorderAction(eventType, actionInContext) {
    try {
      console.log(`🎬 收到录制器事件: ${eventType}`);

      // 🎯 去重逻辑：基于动作内容生成唯一ID
      const actionId = this._generateActionId(actionInContext);

      // 检查是否是重复动作
      if (this._lastActionId === actionId && eventType === 'actionAdded') {
        console.log('⚠️ 跳过重复动作:', actionId);
        return;
      }

      // 更新最后一个动作ID
      this._lastActionId = actionId;

      // 使用Playwright官方的JSONL生成器生成JSON代码
      const jsonCode = this._generateJsonFromAction(actionInContext);

      console.log('📡 生成的JSON代码:', jsonCode);

      // 发送到渲染进程
      if (this.recorder.mainWindow && !this.recorder.mainWindow.isDestroyed()) {
        this.recorder.mainWindow.webContents.send('playwright-code-generated', {
          type: 'json',
          code: jsonCode,
          eventType,
          actionId,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('❌ 处理录制器动作事件失败:', error);
    }
  }

  /**
   * 处理录制器信号事件
   * @param {string} eventType - 事件类型
   * @param {Object} signalInContext - 信号上下文
   * @private
   */
  _handleRecorderSignal(eventType, signalInContext) {
    try {
      console.log(`🎬 收到录制器信号: ${eventType}`, signalInContext);
      // 可以根据需要处理信号事件
    } catch (error) {
      console.error('❌ 处理录制器信号事件失败:', error);
    }
  }

  /**
   * 生成初始化JSON数据（模拟1.53.1版本的行为）
   * @private
   */
  async _generateInitialJsonData() {
    try {
      console.log('🎯 生成初始化JSON数据...');

      // 1. 浏览器配置数据
      const browserConfig = {
        name: 'context-options',
        browserName: this.recorder.browser._name || 'chromium',
        options: {
          ...this.recorder.context._options,
          // 清理敏感信息
          userDataDir: undefined,
          executablePath: undefined
        },
        platform: process.platform,
        sdkLanguage: 'javascript',
        testIdAttributeName: 'data-testid',
        timestamp: Date.now()
      };

      // 2. 语言生成器配置
      const languageConfig = {
        name: 'language-config',
        language: 'jsonl',
        mode: 'recording',
        recorderMode: 'api',
        timestamp: Date.now()
      };

      // 发送初始化数据
      if (this.recorder.mainWindow && !this.recorder.mainWindow.isDestroyed()) {
        // 发送浏览器配置
        this.recorder.mainWindow.webContents.send('playwright-code-generated', {
          type: 'json',
          code: JSON.stringify(browserConfig, null, 2),
          eventType: 'initialization',
          actionId: 'init_browser_config',
          timestamp: Date.now()
        });

        // 发送语言配置
        this.recorder.mainWindow.webContents.send('playwright-code-generated', {
          type: 'json',
          code: JSON.stringify(languageConfig, null, 2),
          eventType: 'initialization',
          actionId: 'init_language_config',
          timestamp: Date.now()
        });
      }

      console.log('✅ 初始化JSON数据已生成');
    } catch (error) {
      console.error('❌ 生成初始化JSON数据失败:', error);
    }
  }

  /**
   * 生成动作唯一ID用于去重
   * @param {Object} actionInContext - 动作上下文
   * @returns {string} 动作唯一ID
   * @private
   */
  _generateActionId(actionInContext) {
    try {
      const action = actionInContext.action;
      const frame = actionInContext.frame;

      // 基于动作类型、选择器、URL等生成唯一ID
      const idComponents = [
        action.name,
        action.selector || '',
        action.url || '',
        frame.pageGuid,
        JSON.stringify(action.options || {}),
        this._actionSequence++  // 确保序列唯一性
      ];

      return idComponents.join('|');
    } catch (error) {
      console.warn('⚠️ 生成动作ID失败:', error);
      return `fallback_${Date.now()}_${Math.random()}`;
    }
  }

  /**
   * 使用Playwright官方API生成JSON代码
   * @param {Object} actionInContext - 动作上下文
   * @returns {string} JSON格式的代码
   * @private
   */
  _generateJsonFromAction(actionInContext) {
    try {
      // 🎯 使用Playwright官方的JsonlLanguageGenerator逻辑
      let locator = undefined;

      // 生成locator（如果有selector）
      if (actionInContext.action && actionInContext.action.selector) {
        try {
          // 尝试使用Playwright的locator生成器
          const playwrightCore = require('playwright-core');
          if (playwrightCore.utils && playwrightCore.utils.asLocator) {
            locator = JSON.parse(playwrightCore.utils.asLocator('jsonl', actionInContext.action.selector));
          } else {
            // 降级处理：简单的locator对象
            locator = {
              kind: 'default',
              body: actionInContext.action.selector,
              options: {}
            };
          }
        } catch (locatorError) {
          console.warn('⚠️ 生成locator失败，使用简单格式:', locatorError.message);
          locator = {
            kind: 'default',
            body: actionInContext.action.selector,
            options: {}
          };
        }
      }

      // 构建JSON条目
      const entry = {
        ...actionInContext.action,
        ...actionInContext.frame,
        locator,
        timestamp: Date.now()
      };

      return JSON.stringify(entry, null, 2);
    } catch (error) {
      console.error('❌ 生成JSON代码失败:', error);
      // 降级处理：直接返回原始数据
      return JSON.stringify(actionInContext, null, 2);
    }
  }

  /**
   * 获取API模式的所有可用特性和API
   * @returns {Object} API模式特性说明
   */
  getApiModeFeatures() {
    return {
      // 🎯 基本特性
      features: {
        noUI: true,                    // 无UI界面
        programmaticControl: true,     // 编程式控制
        eventDriven: true,            // 事件驱动
        realTimeEvents: true,         // 实时事件
        customEventHandlers: true     // 自定义事件处理器
      },

      // 🎯 可用的事件类型
      events: {
        actionAdded: 'action: ActionInContext',      // 动作添加
        actionUpdated: 'action: ActionInContext',    // 动作更新
        signalAdded: 'signal: SignalInContext',      // 信号添加
        modeChanged: 'mode: Mode',                   // 模式变化
        pausedStateChanged: 'paused: boolean',      // 暂停状态变化
        elementPicked: 'elementInfo: ElementInfo',   // 元素选择
        callLogsUpdated: 'callLogs: CallLog[]',     // 调用日志更新
        userSourcesChanged: 'sources: Source[]',    // 用户源码变化
        pageNavigated: 'url: string',               // 页面导航
        contextClosed: 'void'                       // 上下文关闭
      },

      // 🎯 可用的API方法
      apis: {
        recorder: {
          setMode: 'mode: Mode',                     // 设置录制模式
          setHighlightedSelector: 'selector: string', // 设置高亮选择器
          setHighlightedAriaTemplate: 'template: AriaTemplateNode', // 设置ARIA模板
          url: '() => string | undefined',           // 获取当前URL
          updateCallLog: 'metadata: CallMetadata[]'  // 更新调用日志
        },
        context: {
          emit: 'event: RecorderEvent',              // 发出事件
          on: 'listener: EventListener',             // 监听事件
          pages: '() => Page[]',                     // 获取页面列表
          newPage: '() => Promise<Page>'             // 创建新页面
        }
      },

      // 🎯 支持的动作类型
      actionTypes: [
        'openPage', 'navigate', 'click', 'fill', 'press', 'select',
        'check', 'uncheck', 'hover', 'focus', 'blur', 'scroll',
        'drag', 'drop', 'upload', 'download', 'screenshot',
        'assert', 'wait', 'evaluate', 'reload', 'goBack', 'goForward'
      ],

      // 🎯 支持的信号类型
      signalTypes: [
        'popup', 'download', 'dialog', 'console', 'pageerror',
        'request', 'response', 'websocket', 'worker'
      ],

      // 🎯 代码生成器
      codeGenerators: {
        jsonl: 'JsonlLanguageGenerator',           // JSON Lines格式
        javascript: 'JavaScriptLanguageGenerator', // JavaScript格式
        python: 'PythonLanguageGenerator',         // Python格式
        csharp: 'CSharpLanguageGenerator',         // C#格式
        java: 'JavaLanguageGenerator'              // Java格式
      }
    };
  }

  /**
   * 创建页面
   * @private
   */
  async _createPage() {
    this.recorder.page = await this.recorder.context.newPage();
  }

  /**
   * 设置 context 级别的通信 - 适用于所有页面
   * @private
   */
  async _setupContextCommunication() {
    try {
      // 确保 messageHandler 存在
      if (!this.recorder.messageHandler) {
        console.warn('⚠️ MessageHandler 未初始化，跳过通信设置');
        return;
      }

      // 创建 context 级别的通信管理器
      this.contextCommunicationManager = new ContextCommunicationManager(
        this.recorder.context, 
        this.recorder.messageHandler.handlePageMessage.bind(this.recorder.messageHandler)
      );
      
      await this.contextCommunicationManager.initialize();
      
      console.log('📡 Context 级别通信已设置 - 适用于所有页面');
    } catch (error) {
      console.error('❌ Context 通信设置失败:', error);
    }
  }

  /**
   * 设置网络监听 - 使用 Context 级别监听，自动覆盖所有页面
   * @private
   */
  async _setupNetworkMonitoring() {
    // 使用 Context 级别的网络监听，自动覆盖所有页面（包括新标签页）
    await this.networkMonitor.setupContextNetworkMonitoring(this.recorder.context);
  }

  /**
   * 清理浏览器资源
   */
  async cleanup() {
    // 清理 context 通信管理器
    if (this.contextCommunicationManager) {
      try {
        await this.contextCommunicationManager.destroy();
      } catch (error) {
        console.warn('⚠️ 清理 context 通信管理器失败:', error);
      }
    }

    // 清理网络监听
    if (this.networkMonitor) {
      this.networkMonitor.cleanup();
    }
    
    if (this.recorder.browser) {
      await this.recorder.browser.close();
      this.recorder.browser = null;
      this.recorder.context = null;
      this.recorder.page = null;
    }
  }

  /**
   * 获取所有活动页面
   */
  getAllPages() {
    if (this.recorder.context) {
      return this.recorder.context.pages();
    }
    return [];
  }
}

module.exports = PlaywrightManager;
