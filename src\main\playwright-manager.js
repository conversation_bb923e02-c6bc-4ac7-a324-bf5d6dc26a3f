/**
 * Playwright 管理器 - 管理 Playwright 浏览器和录制器
 */

const { chromium } = require('playwright');
const path = require('path');
const { APP_CONFIG, SUCCESS_MESSAGES } = require('./constants');
const NetworkMonitor = require('./network-monitor');
const ContextCommunicationManager = require('./context-communication-manager');

class PlaywrightManager {
  constructor(recorder) {
    this.recorder = recorder;
    this.networkMonitor = new NetworkMonitor(recorder);
    
    // 使用 context 级别的通信管理器
    this.contextCommunicationManager = null;
  }

  /**
   * 初始化 Playwright 录制器
   */
  async initialize() {
    await this._launchBrowser();
    await this._createBrowserContext();
    await this._enableOfficialRecorder();
    await this._createPage();
    await this._setupContextCommunication();
    await this._setupNetworkMonitoring();
    
    this.recorder.isRecording = true;
    console.log(SUCCESS_MESSAGES.PLAYWRIGHT_INITIALIZED);
  }

  /**
   * 启动浏览器
   * @private
   */
  async _launchBrowser() {
    const args = [
      `--app-name=${this.recorder.options.windowTitle}`,
      ...APP_CONFIG.BROWSER_ARGS
    ];

    this.recorder.browser = await chromium.launch({
      headless: false,
      args
    });
  }

  /**
   * 创建浏览器上下文
   * @private
   */
  async _createBrowserContext() {
    this.recorder.context = await this.recorder.browser.newContext({
      viewport: null,
      /* recordVideo: {
        dir: path.join(__dirname, '../../recordings/'),
        size: { width: 1280, height: 720 }
      } */
    });
  }

  /**
   * 启用官方录制器
   * @private
   */
  async _enableOfficialRecorder() {
    console.log('🎭 开始启用官方录制器...');
    console.log('📋 录制器配置:', {
      language: 'jsonl',  // 使用JSONL语言生成器
      mode: 'recording',
      outputFile: this.recorder.options.outputFile,
      recorderMode: 'api'  // 使用API模式
    });

    try {
      // 🎯 使用API模式，避免UI界面，并监听事件生成JSON代码
      await this.recorder.context._enableRecorder({
        language: 'jsonl',  // 使用JSONL语言生成器
        mode: 'recording',
        outputFile: this.recorder.options.outputFile,
        handleSIGINT: false,
        launchOptions: { headless: false },
        contextOptions: {},
        recorderMode: 'api'  // 使用API模式
      }, {
        // 🎯 监听录制器事件并生成JSON代码
        actionAdded: (_page, actionInContext) => {
          this._handleRecorderAction('actionAdded', actionInContext);
        },
        actionUpdated: (_page, actionInContext) => {
          this._handleRecorderAction('actionUpdated', actionInContext);
        },
        signalAdded: (_page, signalInContext) => {
          this._handleRecorderSignal('signalAdded', signalInContext);
        }
      });

      console.log('✅ 官方录制器已启用 (API模式)');
    } catch (error) {
      console.error('❌ 启用官方录制器失败:', error);
      throw error;
    }
  }

  /**
   * 处理录制器动作事件
   * @param {string} eventType - 事件类型
   * @param {Object} actionInContext - 动作上下文
   * @private
   */
  _handleRecorderAction(eventType, actionInContext) {
    try {
      console.log(`🎬 收到录制器事件: ${eventType}`);

      // 使用Playwright官方的JSONL生成器生成JSON代码
      const jsonCode = this._generateJsonFromAction(actionInContext);

      console.log('📡 生成的JSON代码:', jsonCode);

      // 发送到渲染进程
      if (this.recorder.mainWindow && !this.recorder.mainWindow.isDestroyed()) {
        this.recorder.mainWindow.webContents.send('playwright-code-generated', {
          type: 'json',
          code: jsonCode,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('❌ 处理录制器动作事件失败:', error);
    }
  }

  /**
   * 处理录制器信号事件
   * @param {string} eventType - 事件类型
   * @param {Object} signalInContext - 信号上下文
   * @private
   */
  _handleRecorderSignal(eventType, signalInContext) {
    try {
      console.log(`🎬 收到录制器信号: ${eventType}`, signalInContext);
      // 可以根据需要处理信号事件
    } catch (error) {
      console.error('❌ 处理录制器信号事件失败:', error);
    }
  }

  /**
   * 使用Playwright官方API生成JSON代码
   * @param {Object} actionInContext - 动作上下文
   * @returns {string} JSON格式的代码
   * @private
   */
  _generateJsonFromAction(actionInContext) {
    try {
      // 🎯 使用Playwright官方的JsonlLanguageGenerator逻辑
      let locator = undefined;

      // 生成locator（如果有selector）
      if (actionInContext.action && actionInContext.action.selector) {
        try {
          // 尝试使用Playwright的locator生成器
          const playwrightCore = require('playwright-core');
          if (playwrightCore.utils && playwrightCore.utils.asLocator) {
            locator = JSON.parse(playwrightCore.utils.asLocator('jsonl', actionInContext.action.selector));
          } else {
            // 降级处理：简单的locator对象
            locator = {
              kind: 'default',
              body: actionInContext.action.selector,
              options: {}
            };
          }
        } catch (locatorError) {
          console.warn('⚠️ 生成locator失败，使用简单格式:', locatorError.message);
          locator = {
            kind: 'default',
            body: actionInContext.action.selector,
            options: {}
          };
        }
      }

      // 构建JSON条目
      const entry = {
        ...actionInContext.action,
        ...actionInContext.frame,
        locator,
        timestamp: Date.now()
      };

      return JSON.stringify(entry, null, 2);
    } catch (error) {
      console.error('❌ 生成JSON代码失败:', error);
      // 降级处理：直接返回原始数据
      return JSON.stringify(actionInContext, null, 2);
    }
  }

  /**
   * 创建页面
   * @private
   */
  async _createPage() {
    this.recorder.page = await this.recorder.context.newPage();
  }

  /**
   * 设置 context 级别的通信 - 适用于所有页面
   * @private
   */
  async _setupContextCommunication() {
    try {
      // 确保 messageHandler 存在
      if (!this.recorder.messageHandler) {
        console.warn('⚠️ MessageHandler 未初始化，跳过通信设置');
        return;
      }

      // 创建 context 级别的通信管理器
      this.contextCommunicationManager = new ContextCommunicationManager(
        this.recorder.context, 
        this.recorder.messageHandler.handlePageMessage.bind(this.recorder.messageHandler)
      );
      
      await this.contextCommunicationManager.initialize();
      
      console.log('📡 Context 级别通信已设置 - 适用于所有页面');
    } catch (error) {
      console.error('❌ Context 通信设置失败:', error);
    }
  }

  /**
   * 设置网络监听 - 使用 Context 级别监听，自动覆盖所有页面
   * @private
   */
  async _setupNetworkMonitoring() {
    // 使用 Context 级别的网络监听，自动覆盖所有页面（包括新标签页）
    await this.networkMonitor.setupContextNetworkMonitoring(this.recorder.context);
  }

  /**
   * 清理浏览器资源
   */
  async cleanup() {
    // 清理 context 通信管理器
    if (this.contextCommunicationManager) {
      try {
        await this.contextCommunicationManager.destroy();
      } catch (error) {
        console.warn('⚠️ 清理 context 通信管理器失败:', error);
      }
    }

    // 清理网络监听
    if (this.networkMonitor) {
      this.networkMonitor.cleanup();
    }
    
    if (this.recorder.browser) {
      await this.recorder.browser.close();
      this.recorder.browser = null;
      this.recorder.context = null;
      this.recorder.page = null;
    }
  }

  /**
   * 获取所有活动页面
   */
  getAllPages() {
    if (this.recorder.context) {
      return this.recorder.context.pages();
    }
    return [];
  }
}

module.exports = PlaywrightManager;
