import React, { useState, useEffect } from 'react';

// JSON查看器组件，支持右键菜单
const JsonViewer = ({ data, onFieldRightClick, parentPath = '' }) => {
  // 添加数据验证
  if (data === null || data === undefined) {
    return (
      <div className="json-viewer">
        <span className="json-null">null</span>
      </div>
    );
  }

  const renderJsonValue = (key, value, currentPath) => {
    const fullPath = parentPath ? `${parentPath}.${key}` : key;

    if (value === null) {
      return (
        <span 
          className="json-null"
          onContextMenu={(e) => {
            e.preventDefault();
            onFieldRightClick && onFieldRightClick(fullPath, value, e);
          }}
        >
          null
        </span>
      );
    }

    if (typeof value === 'boolean') {
      return (
        <span 
          className="json-boolean"
          onContextMenu={(e) => {
            e.preventDefault();
            onFieldRightClick && onFieldRightClick(fullPath, value, e);
          }}
        >
          {value.toString()}
        </span>
      );
    }

    if (typeof value === 'number') {
      return (
        <span 
          className="json-number"
          onContextMenu={(e) => {
            e.preventDefault();
            onFieldRightClick && onFieldRightClick(fullPath, value, e);
          }}
        >
          {value}
        </span>
      );
    }

    if (typeof value === 'string') {
      return (
        <span 
          className="json-string"
          onContextMenu={(e) => {
            e.preventDefault();
            onFieldRightClick && onFieldRightClick(fullPath, value, e);
          }}
        >
          "{value}"
        </span>
      );
    }

    if (Array.isArray(value)) {
      return (
        <div className="json-array">
          <span className="json-bracket">[</span>
          <div className="json-array-content">
            {value.map((item, index) => (
              <div key={index} className="json-array-item">
                <span className="json-key">{index}:</span>
                {renderJsonValue(index.toString(), item, fullPath)}
                {index < value.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-bracket">]</span>
        </div>
      );
    }

    if (typeof value === 'object') {
      return (
        <div className="json-object">
          <span className="json-bracket">{'{'}</span>
          <div className="json-object-content">
            {Object.entries(value).map(([objKey, objValue], index, array) => (
              <div key={objKey} className="json-object-item">
                <span 
                  className="json-key"
                  onContextMenu={(e) => {
                    e.preventDefault();
                    onFieldRightClick && onFieldRightClick(fullPath, objValue, e);
                  }}
                >
                  "{objKey}":
                </span>
                {renderJsonValue(objKey, objValue, fullPath)}
                {index < array.length - 1 && <span className="json-comma">,</span>}
              </div>
            ))}
          </div>
          <span className="json-bracket">{'}'}</span>
        </div>
      );
    }

    return <span className="json-unknown">{String(value)}</span>;
  };

  return (
    <div className="json-viewer">
      {typeof data === 'object' && data !== null ? (
        <div className="json-root">
          {Object.entries(data).map(([key, value], index, array) => (
            <div key={key} className="json-root-item">
              <span 
                className="json-key"
                onContextMenu={(e) => {
                  e.preventDefault();
                  onFieldRightClick && onFieldRightClick(key, value, e);
                }}
              >
                "{key}":
              </span>
              {renderJsonValue(key, value, '')}
              {index < array.length - 1 && <span className="json-comma">,</span>}
            </div>
          ))}
        </div>
      ) : (
        renderJsonValue('root', data, '')
      )}
    </div>
  );
};

function App() {
  const [isRecording, setIsRecording] = useState(true);
  const [generatedCode, setGeneratedCode] = useState('');
  const [codeLength, setCodeLength] = useState(0);
  const [targetUrl, setTargetUrl] = useState('C:\\Users\\<USER>\\Desktop\\playwright-test\\comprehensive-test-page.html');
  const [selectedElement, setSelectedElement] = useState(null);
  const [lastUpdate, setLastUpdate] = useState('--');
  const [recordedActions, setRecordedActions] = useState([]);
  const [isInspectMode, setIsInspectMode] = useState(false);
  const [currentMode, setCurrentMode] = useState('recording');
  const [recorderState, setRecorderState] = useState({
    mode: 'recording',
    isRecording: true,
    isInspecting: false,
    language: 'javascript',
    actionSelector: null
  });

  // 回放相关状态
  const [isReplaying, setIsReplaying] = useState(false);
  const [replayStatus, setReplayStatus] = useState('');
  const [jsonData, setJsonData] = useState('');

  // 新增：代码格式切换状态
  const [codeFormat, setCodeFormat] = useState('js'); // 'js', 'json', 'script'

  // 新增：选择器类型切换状态
  const [activeSelectorType, setActiveSelectorType] = useState('playwright'); // 'playwright', 'css', 'xpath'

  // 新增：Tab导航状态
  const [activeTab, setActiveTab] = useState('control'); // 'control', 'replay', 'code', 'network', 'output'

  // 新增：Network Tab相关状态
  const [networkRequests, setNetworkRequests] = useState([]);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [networkVariables, setNetworkVariables] = useState([]);
  const [loginCheckpoints, setLoginCheckpoints] = useState([]);

  // 新增：检查点和数据提取相关状态
  const [assertions, setAssertions] = useState([]); // 存储断言检查点
  const [extractedData, setExtractedData] = useState([]); // 存储提取的数据

  // 新增：选中元素弹窗相关状态
  const [showElementModal, setShowElementModal] = useState(false);
  const [elementName, setElementName] = useState(''); // 元素命名

  // 新增：预定义数据类型状态
  const [predefinedData, setPredefinedData] = useState({
    username: { name: '用户名', value: '', selector: '', element: null, timestamp: null },
    userId: { name: '用户ID', value: '', selector: '', element: null, timestamp: null },
    userAvatar: { name: '用户头像', value: '', selector: '', element: null, timestamp: null }
  });

  // 新增：当前选择的数据类型
  const [selectedDataType, setSelectedDataType] = useState('');

  // 新增：断言监听状态
  const [assertionListening, setAssertionListening] = useState(false);
  const [currentAssertionType, setCurrentAssertionType] = useState('');
  const [assertionModal, setAssertionModal] = useState(null);

  // 新增：登录检查点监听状态
  const [loginCheckpointListening, setLoginCheckpointListening] = useState(false);
  const [loginCheckpointModal, setLoginCheckpointModal] = useState(null);

  // 新增：脚本配置状态
  const [scriptConfig, setScriptConfig] = useState({
    name: '未命名脚本',
    type: 'UI自动化',
    executionMode: 'strict'
  });

  useEffect(() => {
    if (window.electronAPI) {
      // 录制器状态更新
      window.electronAPI.onRecorderStatusUpdate((data) => {
        console.log('🎬 录制器状态更新:', data);

        if (data.isRecording !== undefined) {
          setIsRecording(data.isRecording);
        }
        if (data.generatedCode !== undefined) {
          setGeneratedCode(data.generatedCode);
        }
        if (data.codeLength !== undefined) {
          setCodeLength(data.codeLength);
        }

        if (data.mode !== undefined) {
          setCurrentMode(data.mode);
          setIsInspectMode(data.isInspecting || false);
        }

        setRecorderState(prevState => ({
          ...prevState,
          mode: data.mode || prevState.mode,
          isRecording: data.isRecording !== undefined ? data.isRecording : prevState.isRecording,
          isInspecting: data.isInspecting !== undefined ? data.isInspecting : prevState.isInspecting,
          language: data.state?.language || prevState.language,
          actionSelector: data.state?.actionSelector || prevState.actionSelector
        }));

        if (data.timestamp) {
          setLastUpdate(new Date(data.timestamp).toLocaleTimeString());
        }
      });

      // 代码更新
      window.electronAPI.onCodeUpdated((data) => {
        setGeneratedCode(data.code);
        setCodeLength(data.length);
        setLastUpdate(new Date(data.timestamp).toLocaleTimeString());
        console.log('Code updated:', data);
      });

      // Playwright 官方代码生成
      window.electronAPI.onPlaywrightCodeGenerated((data) => {
        console.log('🎭 收到 Playwright 官方代码生成数据:', data);

        if (data.type === 'json' && data.code) {
          try {
            const newAction = JSON.parse(data.code);
            
            // 更新JSON数据用于回放
            setJsonData(prev => prev + data.code + '\n');
            
            // 更新录制的动作列表
            setRecordedActions(prev => [...prev, newAction]);
            
            // 更新代码长度和时间戳
            setCodeLength(prev => prev + data.code.length + 1);
            setLastUpdate(new Date(data.timestamp).toLocaleTimeString());

            console.log('📊 更新JSON数据和录制动作列表');
          } catch (error) {
            console.error('❌ 解析JSON代码失败:', error);
          }
        }
      });

      // 回放状态更新
      window.electronAPI.onReplayStatusUpdate((status) => {
        console.log('🎬 回放状态更新:', status);
        setReplayStatus(status.message || '');
        setIsReplaying(status.status === 'starting' || status.status === 'running');

        if (status.status === 'completed') {
          setTimeout(() => {
            setReplayStatus('');
            setIsReplaying(false);
          }, 3000);
        } else if (status.status === 'error') {
          setTimeout(() => {
            setReplayStatus('');
            setIsReplaying(false);
          }, 5000);
        }
      });

      // 元素选择
      window.electronAPI.onElementSelected((data) => {
        setSelectedElement(data.elementInfo);
        setShowElementModal(true); // 显示弹窗
        
        // 自动选择第一个可用的选择器类型
        if (data.elementInfo?.selectors) {
          const selectors = data.elementInfo.selectors;
          if (selectors.playwright) {
            setActiveSelectorType('playwright');
          } else if (selectors.css) {
            setActiveSelectorType('css');
          } else if (selectors.xpath) {
            setActiveSelectorType('xpath');
          }
        }
        
        // 检查是否有待处理的数据提取操作
        if (window.currentAction === 'extractData') {
          // 数据提取操作保持弹窗，让用户命名
          // createDataExtraction会在保存按钮点击时调用
          window.currentAction = null; // 清除标记
        } else if (window.currentAction === 'extractPredefinedData') {
          // 预定义数据提取操作 - 显示弹窗并自动填充变量名
          setElementName(predefinedData[window.currentDataType].name);
          // 不清除标记，保持在预定义数据提取模式
        }
        
        console.log('Element selected:', data);
      });

      // 模式变化
      window.electronAPI.onModeChanged((data) => {
        setIsInspectMode(data.isInspectMode);
        setCurrentMode(data.mode);
        setIsRecording(data.isRecordingMode);
        console.log('Mode changed:', data);
      });

      // 录制页面消息
      window.electronAPI.onRecorderPageMessage((data) => {
        console.log('Recorder page message:', data);
        if (data.type === 'elementSelected') {
          setSelectedElement(data.data.elementInfo);
          setShowElementModal(true); // 显示弹窗
          
          // 自动选择第一个可用的选择器类型
          if (data.data.elementInfo?.selectors) {
            const selectors = data.data.elementInfo.selectors;
            if (selectors.playwright) {
              setActiveSelectorType('playwright');
            } else if (selectors.css) {
              setActiveSelectorType('css');
            } else if (selectors.xpath) {
              setActiveSelectorType('xpath');
            }
          }
          
          // 检查是否有待处理的数据提取操作
          if (window.currentAction === 'extractData') {
            // 数据提取操作保持弹窗，让用户命名
            // createDataExtraction会在保存按钮点击时调用
            window.currentAction = null; // 清除标记
          } else if (window.currentAction === 'extractPredefinedData') {
            // 预定义数据提取操作 - 显示弹窗并自动填充变量名
            setElementName(predefinedData[window.currentDataType].name);
            // 不清除标记，保持在预定义数据提取模式
          }
        } else if (data.type === 'modeChanged') {
          setIsInspectMode(data.data.isInspectMode);
          setCurrentMode(data.data.mode);
          setIsRecording(data.data.isRecordingMode);
        }
      });

      // 动作录制
      window.electronAPI.onActionRecorded((action) => {
        setRecordedActions(prev => [...prev, action]);
        console.log('Action recorded:', action);
      });

      // 网络请求监听
      window.electronAPI.onNetworkRequestCaptured((requestData) => {
        setNetworkRequests(prev => [requestData, ...prev]);
        console.log('Network request captured:', requestData);
      });

      // 录制动作拦截监听
      window.electronAPI.onRecordActionIntercepted((actionData) => {
        console.log('🎬 收到录制动作拦截数据:', actionData);
        
        // 检查是否在断言监听模式
        if (assertionListening && actionData.detailedInfo) {
          console.log('🎯 处理断言数据（通过录制动作拦截）:', actionData);
          
          // 使用录制动作拦截的详细信息处理断言
          handleAssertionDataFromRecord(actionData);
          
          // 解除监听
          setAssertionListening(false);
          setCurrentAssertionType('');
        }
        
        // 检查是否在登录检查点监听模式
        if (loginCheckpointListening && actionData.detailedInfo) {
          console.log('🔐 处理登录检查点数据（通过录制动作拦截）:', actionData);
          
          // 使用录制动作拦截的详细信息处理登录检查点
          handleLoginCheckpointDataFromRecord(actionData);
          
          // 解除监听
          setLoginCheckpointListening(false);
          setCurrentAssertionType('');
        }
        
        // 如果有详细的元素信息，可以更新选中元素
        if (actionData.detailedInfo) {
          console.log('📊 更新选中元素信息:', actionData.detailedInfo);
          // 可以选择是否更新 selectedElement
          // setSelectedElement(actionData.detailedInfo);
        }
        
        // 如果有错误，显示错误信息
        if (actionData.error) {
          console.error('❌ 录制动作拦截错误:', actionData.error);
        }
      });

      // 获取初始状态
      const getInitialStatus = async (retries = 3) => {
        try {
          const status = await window.electronAPI.getRecorderStatus();
          setIsRecording(status.isRecording);
          setGeneratedCode(status.generatedCode);
          setCodeLength(status.codeLength);
          setSelectedElement(status.selectedElement);
          console.log('✅ 成功获取初始录制器状态:', status);
        } catch (error) {
          console.warn(`⚠️ 获取录制器状态失败 (剩余重试次数: ${retries - 1}):`, error.message);
          if (retries > 1) {
            setTimeout(() => getInitialStatus(retries - 1), 1000);
          } else {
            console.error('❌ 无法获取录制器状态，已达到最大重试次数');
            setIsRecording(false);
            setGeneratedCode('// 等待连接到录制器...');
            setCodeLength(0);
          }
        }
      };

      setTimeout(() => getInitialStatus(), 500);
    }

    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners('recorder-status-update');
        window.electronAPI.removeAllListeners('code-updated');
        window.electronAPI.removeAllListeners('element-selected');
        window.electronAPI.removeAllListeners('mode-changed');
        window.electronAPI.removeAllListeners('recorder-page-message');
        window.electronAPI.removeAllListeners('action-recorded');
        window.electronAPI.removeAllListeners('network-request-captured');
      }
    };
  }, [assertionListening, loginCheckpointListening, currentAssertionType]);

  // 新增：处理断言数据
  const handleAssertionData = (data) => {
    console.log('🎯 处理断言数据:', data);
    
    // 获取JSON数据的最后一条
    if (data.sources) {
      const jsonSource = data.sources.find(source =>
        source.language === 'javascript' && source.id === 'jsonl' ||
        source.label?.toLowerCase().includes('jsonl') ||
        source.label?.toLowerCase().includes('json')
      );
      
      if (jsonSource && jsonSource.text) {
        const lines = jsonSource.text.trim().split('\n');
        const lastLine = lines[lines.length - 1];
        
        try {
          const assertionData = JSON.parse(lastLine);
          console.log('📄 解析的断言数据:', assertionData);
          
          // 显示断言弹窗
          showAssertionModal(assertionData, data.mode);
          
        } catch (error) {
          console.error('❌ 解析JSON数据失败:', error);
        }
      }
    }
  };

  // 新增：处理来自录制动作拦截的断言数据
  const handleAssertionDataFromRecord = (actionData) => {
    console.log('🎯 处理录制动作拦截的断言数据:', actionData);
    
    if (actionData.detailedInfo) {
      const assertionTypeMap = {
        'visibility': { type: 'visibility', name: '可见性断言', icon: '👁️' },
        'text': { type: 'text', name: '文本断言', icon: '📝' },
        'value': { type: 'value', name: '值断言', icon: '💎' }
      };
      
      const assertionInfo = assertionTypeMap[currentAssertionType] || { type: 'unknown', name: '未知断言', icon: '❓' };
      
      // 使用录制动作拦截的详细信息创建断言弹窗
      showAssertionModalFromRecord(actionData, assertionInfo);
    }
  };

  // 新增：显示断言弹窗
  const showAssertionModal = (assertionData, mode) => {
    const assertionTypeMap = {
      'assertingVisibility': { type: 'visibility', name: '可见性断言', icon: '👁️' },
      'assertingText': { type: 'text', name: '文本断言', icon: '📝' },
      'assertingValue': { type: 'value', name: '值断言', icon: '💎' }
    };
    
    const assertionInfo = assertionTypeMap[mode] || { type: 'unknown', name: '未知断言', icon: '❓' };
    
    const modalData = {
      id: Date.now(),
      type: assertionInfo.type,
      name: assertionInfo.name,
      icon: assertionInfo.icon,
      selector: assertionData.selector || '',
      expectedValue: getAssertionExpectedValue(assertionData, assertionInfo.type),
      timestamp: new Date().toLocaleTimeString(),
      rawData: assertionData,
      description: `验证元素${assertionInfo.name}`
    };
    
    setAssertionModal(modalData);
    console.log('📋 显示断言弹窗:', modalData);
  };

  // 新增：显示基于录制动作拦截数据的断言弹窗
  const showAssertionModalFromRecord = (actionData, assertionInfo) => {
    const detailedInfo = actionData.detailedInfo;
    
    const modalData = {
      id: Date.now(),
      type: assertionInfo.type,
      name: assertionInfo.name,
      icon: assertionInfo.icon,
      selector: actionData.selector || '',
      selectors: detailedInfo.selectors || {
        playwright: actionData.selector,
        css: actionData.selector,
        xpath: null
      },
      expectedValue: getAssertionExpectedValueFromRecord(detailedInfo, assertionInfo.type),
      timestamp: new Date().toLocaleTimeString(),
      rawData: actionData,
      detailedInfo: detailedInfo,
      description: `验证元素${assertionInfo.name}`,
      // 标记为来自录制动作拦截的数据
      fromRecord: true
    };
    
    // 自动选择第一个可用的选择器类型
    if (modalData.selectors) {
      if (modalData.selectors.playwright) {
        setActiveSelectorType('playwright');
      } else if (modalData.selectors.css) {
        setActiveSelectorType('css');
      } else if (modalData.selectors.xpath) {
        setActiveSelectorType('xpath');
      }
    }
    
    setAssertionModal(modalData);
    console.log('📋 显示断言弹窗（来自录制动作拦截）:', modalData);
  };

  // 新增：获取断言预期值
  const getAssertionExpectedValue = (data, type) => {
    switch (type) {
      case 'visibility':
        return '可见';
      case 'text':
        return data.text || data.value || '';
      case 'value':
        return data.value || '';
      default:
        return '';
    }
  };

  // 新增：关闭断言弹窗
  const closeAssertionModal = () => {
    setAssertionModal(null);
  };

  // 新增：保存断言数据
  const saveAssertionData = () => {
    if (!assertionModal) return;
    
    // 获取当前选择的选择器
    const currentSelector = assertionModal.fromRecord && assertionModal.selectors
      ? assertionModal.selectors[activeSelectorType]
      : assertionModal.selector;
    
    const assertionItem = {
      id: assertionModal.id,
      name: `${assertionModal.name}_${Date.now()}`,
      type: assertionModal.type,
      selector: currentSelector,
      selectorType: assertionModal.fromRecord ? activeSelectorType : 'default',
      timestamp: assertionModal.timestamp,
      expectedValue: assertionModal.expectedValue,
      description: assertionModal.description,
      rawData: assertionModal.rawData,
      detailedInfo: assertionModal.detailedInfo
    };
    
    setAssertions(prev => [...prev, assertionItem]);
    console.log('✅ 保存断言数据:', assertionItem);
    
    // 关闭弹窗
    closeAssertionModal();
  };

  // 新增：处理登录检查点数据
  const handleLoginCheckpointData = (data) => {
    console.log('🔐 处理登录检查点数据:', data);
    
    // 获取JSON数据的最后一条
    if (data.sources) {
      const jsonSource = data.sources.find(source =>
        source.language === 'javascript' && source.id === 'jsonl' ||
        source.label?.toLowerCase().includes('jsonl') ||
        source.label?.toLowerCase().includes('json')
      );
      
      if (jsonSource && jsonSource.text) {
        const lines = jsonSource.text.trim().split('\n');
        const lastLine = lines[lines.length - 1];
        
        try {
          const checkpointData = JSON.parse(lastLine);
          console.log('📄 解析的登录检查点数据:', checkpointData);
          
          // 显示登录检查点弹窗
          showLoginCheckpointModal(checkpointData, data.mode);
          
        } catch (error) {
          console.error('❌ 解析JSON数据失败:', error);
        }
      }
    }
  };

  // 新增：处理来自录制动作拦截的登录检查点数据
  const handleLoginCheckpointDataFromRecord = (actionData) => {
    console.log('🔐 处理录制动作拦截的登录检查点数据:', actionData);
    console.log('🔍 当前断言类型:', currentAssertionType);
    
    if (actionData.detailedInfo) {
      const checkpointTypeMap = {
        'visibility': { type: 'visibility', name: '可见性检查', icon: '👁️' },
        'text': { type: 'text', name: '文本检查', icon: '📝' },
        'value': { type: 'value', name: '值检查', icon: '💎' }
      };
      
      // 如果 currentAssertionType 为空或无效，尝试从 actionData 推断类型
      let checkpointType = currentAssertionType;
      if (!checkpointType || !checkpointTypeMap[checkpointType]) {
        // 尝试从 actionData 推断类型
        if (actionData.detailedInfo.textContent || actionData.detailedInfo.innerText) {
          checkpointType = 'text';
        } else if (actionData.detailedInfo.value !== undefined) {
          checkpointType = 'value';
        } else {
          checkpointType = 'visibility';
        }
        console.log('🔍 推断的检查点类型:', checkpointType);
      }
      
      const checkpointInfo = checkpointTypeMap[checkpointType] || { type: 'visibility', name: '可见性检查', icon: '👁️' };
      
      console.log('📋 检查点信息:', checkpointInfo);
      
      // 使用录制动作拦截的详细信息创建登录检查点弹窗
      showLoginCheckpointModalFromRecord(actionData, checkpointInfo);
    }
  };

  // 新增：显示登录检查点弹窗
  const showLoginCheckpointModal = (checkpointData, mode) => {
    const checkpointTypeMap = {
      'assertingVisibility': { type: 'visibility', name: '可见性检查', icon: '👁️' },
      'assertingText': { type: 'text', name: '文本检查', icon: '📝' },
      'assertingValue': { type: 'value', name: '值检查', icon: '💎' }
    };
    
    const checkpointInfo = checkpointTypeMap[mode] || { type: 'unknown', name: '未知检查', icon: '❓' };
    
    const modalData = {
      id: Date.now(),
      type: checkpointInfo.type,
      name: `登录成功${checkpointInfo.name}`,
      icon: '🔐',
      selector: checkpointData.selector || '',
      expectedValue: getCheckpointExpectedValue(checkpointData, checkpointInfo.type),
      timestamp: new Date().toLocaleTimeString(),
      rawData: checkpointData,
      description: `验证登录成功后的${checkpointInfo.name}`
    };
    
    setLoginCheckpointModal(modalData);
    console.log('📋 显示登录检查点弹窗:', modalData);
  };

  // 新增：显示基于录制动作拦截数据的登录检查点弹窗
  const showLoginCheckpointModalFromRecord = (actionData, checkpointInfo) => {
    const detailedInfo = actionData.detailedInfo;
    
    const modalData = {
      id: Date.now(),
      type: checkpointInfo.type,
      name: `登录成功${checkpointInfo.name}`,
      icon: '🔐',
      selector: actionData.selector || '',
      selectors: detailedInfo.selectors || {
        playwright: actionData.selector,
        css: actionData.selector,
        xpath: null
      },
      expectedValue: getCheckpointExpectedValueFromRecord(detailedInfo, checkpointInfo.type),
      timestamp: new Date().toLocaleTimeString(),
      rawData: actionData,
      detailedInfo: detailedInfo,
      description: `验证登录成功后的${checkpointInfo.name}`,
      // 标记为来自录制动作拦截的数据
      fromRecord: true
    };
    
    // 自动选择第一个可用的选择器类型
    if (modalData.selectors) {
      if (modalData.selectors.playwright) {
        setActiveSelectorType('playwright');
      } else if (modalData.selectors.css) {
        setActiveSelectorType('css');
      } else if (modalData.selectors.xpath) {
        setActiveSelectorType('xpath');
      }
    }
    
    setLoginCheckpointModal(modalData);
    console.log('📋 显示登录检查点弹窗（来自录制动作拦截）:', modalData);
  };

  // 新增：获取检查点预期值
  const getCheckpointExpectedValue = (data, type) => {
    switch (type) {
      case 'visibility':
        return '元素可见';
      case 'text':
        return data.text || data.value || '';
      case 'value':
        return data.value || '';
      default:
        return '';
    }
  };

  // 新增：获取断言预期值（来自录制动作拦截）
  const getAssertionExpectedValueFromRecord = (detailedInfo, type) => {
    switch (type) {
      case 'visibility':
        return '可见';
      case 'text':
        return detailedInfo.textContent || detailedInfo.innerText || '';
      case 'value':
        return detailedInfo.value || '';
      default:
        return '';
    }
  };

  // 新增：获取检查点预期值（来自录制动作拦截）
  const getCheckpointExpectedValueFromRecord = (detailedInfo, type) => {
    switch (type) {
      case 'visibility':
        return '元素可见';
      case 'text':
        return detailedInfo.textContent || detailedInfo.innerText || '';
      case 'value':
        return detailedInfo.value || '';
      default:
        return '';
    }
  };

  // 新增：关闭登录检查点弹窗
  const closeLoginCheckpointModal = () => {
    setLoginCheckpointModal(null);
  };

  // 新增：保存登录检查点数据
  const saveLoginCheckpointData = () => {
    if (!loginCheckpointModal) return;
    
    // 获取当前选择的选择器
    const currentSelector = loginCheckpointModal.fromRecord && loginCheckpointModal.selectors
      ? loginCheckpointModal.selectors[activeSelectorType]
      : loginCheckpointModal.selector;
    
    const checkpointItem = {
      id: loginCheckpointModal.id,
      name: `${loginCheckpointModal.name}_${Date.now()}`,
      type: loginCheckpointModal.type,
      selector: currentSelector,
      selectorType: loginCheckpointModal.fromRecord ? activeSelectorType : 'default',
      timestamp: loginCheckpointModal.timestamp,
      expectedValue: loginCheckpointModal.expectedValue,
      description: loginCheckpointModal.description,
      rawData: loginCheckpointModal.rawData,
      detailedInfo: loginCheckpointModal.detailedInfo
    };
    
    setLoginCheckpoints(prev => [...prev, checkpointItem]);
    console.log('✅ 保存登录检查点数据:', checkpointItem);
    
    // 关闭弹窗
    closeLoginCheckpointModal();
  };

  // 智能录制切换
  const handleToggleRecord = async () => {
    try {
      const result = await window.electronAPI.recorderControl('toggleRecord');
      if (!result.success) {
        alert('Failed to toggle recording: ' + result.error);
      }
    } catch (error) {
      alert('Error toggling recording: ' + error.message);
    }
  };

  // 智能检查切换
  const handleToggleInspect = async () => {
    try {
      const result = await window.electronAPI.recorderControl('togglePickLocator');
      if (!result.success) {
        alert('Failed to toggle inspect mode: ' + result.error);
      }
    } catch (error) {
      alert('Error toggling inspect mode: ' + error.message);
    }
  };

  const handleNavigate = async () => {
    if (!targetUrl) {
      alert('Please enter a URL');
      return;
    }
    try {
      const result = await window.electronAPI.navigateToUrl(targetUrl);
      if (!result.success) {
        alert('Failed to navigate: ' + result.error);
      }
    } catch (error) {
      alert('Error navigating: ' + error.message);
    }
  };

  const handleExportCode = async () => {
    try {
      const code = await window.electronAPI.exportCode();
      navigator.clipboard.writeText(code).then(() => {
        alert('代码已复制到剪贴板');
      });
    } catch (error) {
      alert('Error exporting code: ' + error.message);
    }
  };

  const handleDebugInfo = async () => {
    try {
      const result = await window.electronAPI.recorderControl('getAvailableMethods');
      alert('调试信息:\n' + JSON.stringify(result, null, 2));
    } catch (error) {
      alert('Error getting debug info: ' + error.message);
    }
  };

  // 回放JSON数据
  const handleReplayJson = async () => {
    if (!jsonData) {
      alert('没有可回放的JSON数据！请先录制一些操作。');
      return;
    }

    try {
      setIsReplaying(true);
      setReplayStatus('正在启动回放...');

      const result = await window.electronAPI.replayJsonData(jsonData);

      if (!result.success) {
        alert('回放失败: ' + result.error);
        setIsReplaying(false);
        setReplayStatus('');
      }
    } catch (error) {
      alert('回放错误: ' + error.message);
      setIsReplaying(false);
      setReplayStatus('');
    }
  };

  // 回放脚本格式数据
  const handleReplayScript = async () => {
    try {
      setIsReplaying(true);
      setReplayStatus('正在启动脚本回放...');

      // 生成脚本数据
      const scriptData = getCurrentScriptData();
      const result = await window.electronAPI.replayScriptData(scriptData);

      if (!result.success) {
        alert('脚本回放失败: ' + result.error);
        setIsReplaying(false);
        setReplayStatus('');
      }
    } catch (error) {
      alert('脚本回放错误: ' + error.message);
      setIsReplaying(false);
      setReplayStatus('');
    }
  };

  // 获取当前脚本数据
  const getCurrentScriptData = () => {
    // 合并断言检查点和登录检查点为一个检查点列表
    const allCheckpoints = [
      ...(assertions || []),
      ...(loginCheckpoints || [])
    ];
    
    // 合并所有网络变量、提取的数据和预定义数据作为输出变量
    const predefinedOutputVariables = Object.entries(predefinedData)
      .filter(([key, data]) => data.value)
      .map(([key, data]) => ({
        id: key,
        name: data.name,
        value: data.value,
        selector: data.selector,
        timestamp: data.timestamp,
        type: 'predefined',
        description: `预定义数据: ${data.name}`
      }));
    
    const allOutputVariables = [
      ...(networkVariables || []),     // 所有网络变量
      ...(extractedData || []),        // 所有提取的数据
      ...predefinedOutputVariables     // 有值的预定义数据
    ];
    
    return {
      name: scriptConfig.name || '未命名脚本',
      type: scriptConfig.type || 'UI自动化',
      executionMode: scriptConfig.executionMode || 'strict',
      checkpoint: allCheckpoints,
      executionSteps: jsonData ? (() => {
        try {
          const lines = jsonData.split('\n').filter(line => line.trim());
          return lines.map(line => {
            try {
              return JSON.parse(line);
            } catch {
              return { raw: line };
            }
          });
        } catch {
          return [];
        }
      })() : [],
      inputVariables: [], // 暂时为空，可以后续扩展
      outputVariables: allOutputVariables
    };
  };

  // 停止回放
  const handleStopReplay = async () => {
    try {
      await window.electronAPI.stopReplay();
      setIsReplaying(false);
      setReplayStatus('回放已停止');
      setTimeout(() => setReplayStatus(''), 2000);
    } catch (error) {
      alert('停止回放失败: ' + error.message);
    }
  };

  // 获取当前显示的代码内容
  const getCurrentCodeContent = () => {
    if (codeFormat === 'json' && jsonData) {
      try {
        // 格式化JSON显示
        const lines = jsonData.split('\n');
        return lines.map(line => {
          try {
            const parsed = JSON.parse(line);
            return JSON.stringify(parsed, null, 2);
          } catch {
            return line;
          }
        }).join('\n\n');
      } catch {
        return jsonData;
      }
    } else if (codeFormat === 'script') {
      // 生成脚本格式的JSON
      
      // 合并断言检查点和登录检查点为一个检查点列表
      const allCheckpoints = [
        ...(assertions || []),
        ...(loginCheckpoints || [])
      ];
      
      // 合并所有网络变量、提取的数据和预定义数据作为输出变量
      const predefinedOutputVariables = Object.entries(predefinedData)
        .filter(([key, data]) => data.value)
        .map(([key, data]) => ({
          id: key,
          name: data.name,
          value: data.value,
          selector: data.selector,
          timestamp: data.timestamp,
          type: 'predefined',
          description: `预定义数据: ${data.name}`
        }));
      
      const allOutputVariables = [
        ...(networkVariables || []),     // 所有网络变量都算作输出变量
        ...(extractedData || []),        // 所有提取的数据都算作输出变量
        ...predefinedOutputVariables     // 有值的预定义数据也算作输出变量
      ];
      
      const scriptData = {
        name: scriptConfig.name || '未命名脚本',
        type: scriptConfig.type || 'UI自动化',
        executionMode: scriptConfig.executionMode || 'strict',
        checkpoint: allCheckpoints,
        executionSteps: jsonData ? (() => {
          try {
            const lines = jsonData.split('\n').filter(line => line.trim());
            return lines.map(line => {
              try {
                return JSON.parse(line);
              } catch {
                return { raw: line };
              }
            });
          } catch {
            return [];
          }
        })() : [],
        inputVariables: [], // 暂时为空，可以后续扩展
        outputVariables: allOutputVariables
      };
      
      return JSON.stringify(scriptData, null, 2);
    }
    return generatedCode || '等待代码生成...';
  };

  // Network Tab相关函数
  const handleRequestClick = (request) => {
    setSelectedRequest(request);
    setContextMenu(null);
  };

  const handleRequestRightClick = (e, request) => {
    e.preventDefault();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      request: request,
      type: 'request'
    });
  };

  const handleResponseFieldRightClick = (e, request, fieldPath, value) => {
    if (e) {
      e.preventDefault();
    }
    
    setContextMenu({
      x: e ? e.clientX : 100, // 如果没有事件，使用默认位置
      y: e ? e.clientY : 100,
      request: request,
      fieldPath: fieldPath,
      value: value,
      type: 'responseField'
    });
  };

  const setAsLoginCheckpoint = (request) => {
    const checkpoint = {
      id: Date.now(),
      name: `登录检查点 - ${request.url.split('/').pop()}`,
      url: request.url,
      method: request.method,
      expectedStatus: request.status,
      description: `基于 ${request.method} ${request.url} 的登录验证`
    };
    setLoginCheckpoints(prev => [...prev, checkpoint]);
    setContextMenu(null);
    console.log('设置登录检查点:', checkpoint);
  };

  const saveAsVariable = (request, fieldPath, value) => {
    const variable = {
      id: Date.now(),
      name: `${request.url.split('/').pop()}_${fieldPath.replace(/\./g, '_')}`,
      source: `${request.method} ${request.url}`,
      fieldPath: fieldPath,
      value: value,
      type: typeof value,
      description: `从 ${request.url} 响应中提取的 ${fieldPath}`
    };
    setNetworkVariables(prev => [...prev, variable]);
    setContextMenu(null);
    console.log('保存为变量:', variable);
  };

  // 处理断言检查点
  const handleAssertAction = async (assertType) => {
    try {
      // 映射到官方的断言模式
      const assertionModeMap = {
        'visibility': 'assertingVisibility',
        'text': 'assertingText', 
        'value': 'assertingValue'
      };
      
      const officialMode = assertionModeMap[assertType];
      if (!officialMode) {
        throw new Error(`不支持的断言类型: ${assertType}`);
      }
      
      // 启动断言监听模式
      setAssertionListening(true);
      setCurrentAssertionType(assertType);
      
      // 调用官方的断言模式
      const result = await window.electronAPI.recorderControl('setMode', { mode: officialMode });
      
      if (!result.success) {
        alert('启动断言模式失败: ' + result.error);
        setAssertionListening(false);
        setCurrentAssertionType('');
        return;
      }
      
      console.log(`🎯 已启动断言监听模式: ${officialMode}`);
    } catch (error) {
      alert('启动断言模式失败: ' + error.message);
      setAssertionListening(false);
      setCurrentAssertionType('');
    }
  };

  // 新增：处理登录检查点
  const handleLoginCheckpointAction = async (checkpointType) => {
    try {
      // 映射到官方的断言模式
      const checkpointModeMap = {
        'visibility': 'assertingVisibility',
        'text': 'assertingText', 
        'value': 'assertingValue'
      };
      
      const officialMode = checkpointModeMap[checkpointType];
      if (!officialMode) {
        throw new Error(`不支持的检查点类型: ${checkpointType}`);
      }
      
      // 设置当前断言类型，用于后续检查点类型识别
      setCurrentAssertionType(checkpointType);
      
      // 启动登录检查点监听模式
      setLoginCheckpointListening(true);
      
      // 调用官方的断言模式
      const result = await window.electronAPI.recorderControl('setMode', { mode: officialMode });
      
      if (!result.success) {
        alert('启动登录检查点模式失败: ' + result.error);
        setLoginCheckpointListening(false);
        setCurrentAssertionType('');
        return;
      }
      
      console.log(`🔐 已启动登录检查点监听模式: ${officialMode}, 检查点类型: ${checkpointType}`);
    } catch (error) {
      alert('启动登录检查点模式失败: ' + error.message);
      setLoginCheckpointListening(false);
      setCurrentAssertionType('');
    }
  };

  // 处理数据提取
  const handleExtractData = async () => {
    try {
      // 先开启检查模式
      await handleToggleInspect();
      
      // 标记为数据提取模式
      window.currentAction = 'extractData';
      
      console.log('🎯 开始数据提取，请选择元素');
    } catch (error) {
      alert('启动元素选择失败: ' + error.message);
    }
  };

  // 新增：处理预定义数据类型提取
  const handlePredefinedDataExtract = async (dataType) => {
    try {
      // 只有在非检查模式时才开启检查模式
      if (!isInspectMode) {
        await handleToggleInspect();
      }
      
      // 标记为预定义数据提取模式
      window.currentAction = 'extractPredefinedData';
      window.currentDataType = dataType;
      
      console.log(`🎯 开始提取${predefinedData[dataType].name}，请选择元素`);
    } catch (error) {
      alert('启动元素选择失败: ' + error.message);
    }
  };

  // 创建数据提取
  const createDataExtraction = (element) => {
    const extractedItem = {
      id: Date.now(),
      name: `${element.tagName}_${element.id || 'element'}_${Date.now()}`,
      selector: element.selectors?.playwright || element.selectors?.css || element.selectors?.xpath,
      timestamp: new Date().toLocaleTimeString(),
      element: {
        tagName: element.tagName,
        textContent: element.textContent || element.innerText,
        value: element.value,
        id: element.id,
        className: element.className,
        href: element.href
      },
      extractedValue: element.textContent || element.innerText || element.value || element.href,
      description: `从 ${element.tagName} 元素提取数据`
    };

    setExtractedData(prev => [...prev, extractedItem]);
    console.log('创建数据提取:', extractedItem);
  };

  // 新增：填充预定义数据
  const fillPredefinedData = (element, dataType) => {
    const extractedValue = element.textContent || element.innerText || element.value || element.href || element.src;
    const selector = element.selectors?.playwright || element.selectors?.css || element.selectors?.xpath;
    
    setPredefinedData(prev => ({
      ...prev,
      [dataType]: {
        ...prev[dataType],
        value: extractedValue,
        selector: selector,
        element: element,
        timestamp: new Date().toLocaleTimeString()
      }
    }));
    
    console.log(`填充预定义数据 ${dataType}:`, extractedValue);
  };

  // 新增：清除预定义数据
  const clearPredefinedData = (dataType) => {
    setPredefinedData(prev => ({
      ...prev,
      [dataType]: {
        ...prev[dataType],
        value: '',
        selector: '',
        element: null,
        timestamp: null
      }
    }));
  };

  // 新增：清除所有预定义数据
  const clearAllPredefinedData = () => {
    setPredefinedData({
      username: { name: '用户名', value: '', selector: '', element: null, timestamp: null },
      userId: { name: '用户ID', value: '', selector: '', element: null, timestamp: null },
      userAvatar: { name: '用户头像', value: '', selector: '', element: null, timestamp: null }
    });
  };

  // 关闭元素弹窗
  const closeElementModal = () => {
    setShowElementModal(false);
    setSelectedElement(null);
    setElementName('');
    
    // 清除所有相关标记
    window.currentAction = null;
    window.currentDataType = null;
  };

  // 保存选中元素到提取数据
  const saveSelectedElement = () => {
    if (!selectedElement) return;
    
    // 检查是否是预定义数据提取模式
    if (window.currentAction === 'extractPredefinedData' && window.currentDataType) {
      // 保存到预定义数据
      fillPredefinedData(selectedElement, window.currentDataType);
      
      // 清除标记
      window.currentAction = null;
      window.currentDataType = null;
      
      console.log('保存到预定义数据:', window.currentDataType);
    } else {
      // 保存到提取数据列表
      const elementName_ = elementName.trim() || `${selectedElement.tagName}_${selectedElement.id || 'element'}_${Date.now()}`;
      
      const extractedItem = {
        id: Date.now(),
        name: elementName_,
        selector: selectedElement.selectors?.playwright || selectedElement.selectors?.css || selectedElement.selectors?.xpath,
        timestamp: new Date().toLocaleTimeString(),
        element: {
          tagName: selectedElement.tagName,
          textContent: selectedElement.textContent || selectedElement.innerText,
          value: selectedElement.value,
          id: selectedElement.id,
          className: selectedElement.className,
          href: selectedElement.href
        },
        extractedValue: selectedElement.textContent || selectedElement.innerText || selectedElement.value || selectedElement.href,
        description: `从 ${selectedElement.tagName} 元素提取数据`
      };

      setExtractedData(prev => [...prev, extractedItem]);
      console.log('保存到提取数据列表:', extractedItem);
    }
    
    // 关闭弹窗
    closeElementModal();
  };

  // 模拟网络请求数据
  const mockNetworkRequests = [
    
  ];

  // 初始化模拟数据
  useEffect(() => {
    setNetworkRequests(mockNetworkRequests);
  }, []);

  // 处理点击空白区域关闭右键菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (contextMenu && !event.target.closest('.context-menu')) {
        setContextMenu(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [contextMenu]);

  // Tab内容渲染函数
  const renderTabContent = () => {
    switch (activeTab) {
      case 'control':
  return (
          <div className="tab-content">
            {/* 录制控制 */}
            <div className="control-group">
              <div className="group-header">
                <span className="group-title">🎛️ 录制控制</span>
        </div>
              <div className="button-row">
                <button
                  className={`icon-btn ${isRecord ? 'active danger' : 'success'}`}
                  onClick={handleToggleRecord}
                  title={`当前: ${recorderState.mode} | 切换录制`}
                >
                  {isRecord ? '⏹️' : '🔴'}
                </button>
                <button
                  className={`icon-btn ${recorderState.isInspecting ? 'active warning' : 'secondary'}`}
                  onClick={handleToggleInspect}
                  title="选择元素"
                >
                  🔍
                </button>
                <button
                  className="icon-btn secondary"
                  onClick={handleDebugInfo}
                  title="调试信息"
                >
                  🐛
                </button>
            </div>
            </div>

            {/* 待办事项 */}
            <div className="control-group">
              <div className="group-header">
                <span className="group-title">📝 待办事项</span>
                <span className="data-info">需要完成的任务</span>
            </div>
              <div className="todo-list">
                <div className="todo-item">
                  <div className="todo-header">
                    <span className="todo-icon">📋</span>
                    <span className="todo-title">提取数据</span>
                    <span className="todo-status optional">可选</span>
            </div>
                  <div className="todo-content">
                    <p className="todo-description">从页面元素中提取数据用于后续测试</p>
                    
                    {/* 预定义数据类型 */}
                    <div className="predefined-data-section">
                      <div className="predefined-data-header">
                        <span className="predefined-title">🎯 预定义数据类型</span>
                        <button 
                          className="clear-all-btn"
                          onClick={clearAllPredefinedData}
                          title="清除所有数据"
                        >
                          🗑️ 清空
                        </button>
            </div>
                      
                      <div className="predefined-data-list">
                        {Object.entries(predefinedData).map(([key, data]) => (
                          <div key={key} className="predefined-data-item">
                            <div className="predefined-data-info">
                              <span className="predefined-data-name">{data.name}</span>
                              <span className="predefined-data-value" title={data.value}>
                                {data.value ? (
                                  data.value.length > 20 ? data.value.substring(0, 20) + '...' : data.value
                                ) : '未设置'}
                </span>
                              {data.timestamp && (
                                <span className="predefined-data-time">{data.timestamp}</span>
            )}
              </div>
                            <div className="predefined-data-actions">
                              <button 
                                className="predefined-extract-btn"
                                onClick={() => handlePredefinedDataExtract(key)}
                                title={`提取${data.name}`}
                              >
                                🎯 提取
                              </button>
                              {data.value && (
                                <button 
                                  className="predefined-clear-btn"
                                  onClick={() => clearPredefinedData(key)}
                                  title="清除数据"
                                >
                                  ✕
                                </button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
          </div>

                    <div className="todo-actions">
              <button
                        className="todo-btn primary" 
                        onClick={handleExtractData}
                        title="选择元素提取数据"
                      >
                        🎯 自定义提取
              </button>
                      <button className="todo-btn secondary" title="查看提取结果">
                        👀 查看结果
                      </button>
                    </div>
                  </div>
                </div>

                {recordedActions.length > 0 && (
                  <div className="todo-item completed">
                    <div className="todo-header">
                      <span className="todo-icon">✅</span>
                      <span className="todo-title">录制操作步骤</span>
                      <span className="todo-status completed">已完成</span>
                    </div>
                    <div className="todo-content">
                      <p className="todo-description">已录制 {recordedActions.length} 个操作步骤</p>
                      <div className="todo-actions">
              <button
                          className="todo-btn success" 
                          onClick={() => setActiveTab('replay')}
                          title="查看录制的操作"
                        >
                          👀 查看操作
                        </button>
                        <button 
                          className="todo-btn secondary"
                          onClick={() => setActiveTab('code')}
                          title="查看生成的代码"
                        >
                          📝 查看代码
              </button>
            </div>
          </div>
                  </div>
                )}

                <div className="todo-item">
                  <div className="todo-header">
                    <span className="todo-icon">🔐</span>
                    <span className="todo-title">登录成功后的检查点</span>
                    <span className="todo-status optional">可选</span>
            </div>
                  <div className="todo-content">
                    <p className="todo-description">设置登录成功后的验证检查点，确保登录状态正确</p>
                    <div className="todo-actions">
            <button
                        className="todo-btn primary" 
                        onClick={() => handleLoginCheckpointAction('visibility')}
                        title="检查登录后元素可见性"
                        disabled={loginCheckpointListening || assertionListening}
                      >
                        👁️ 可见性检查
                      </button>
                      <button 
                        className="todo-btn primary" 
                        onClick={() => handleLoginCheckpointAction('text')}
                        title="检查登录后文本内容"
                        disabled={loginCheckpointListening || assertionListening}
                      >
                        📝 文本检查
                      </button>
                      <button 
                        className="todo-btn primary" 
                        onClick={() => handleLoginCheckpointAction('value')}
                        title="检查登录后元素值"
                        disabled={loginCheckpointListening || assertionListening}
                      >
                        💎 值检查
            </button>
          </div>

                    {/* 登录检查点监听状态提示 */}
                    {loginCheckpointListening && (
                      <div className="checkpoint-listening-status">
                        <div className="listening-icon">🔐</div>
                        <div className="listening-text">
                          <div className="listening-title">
                            正在监听登录检查点...
            </div>
                          <div className="listening-hint">
                            请在页面上操作登录相关元素，系统将自动捕获检查点数据
            </div>
            </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 提取数据列表 */}
            {/* 已移动到输出Tab中 */}
            {/* {extractedData.length > 0 && (
              <div className="control-group">
                <div className="group-header">
                  <span className="group-title">📋 提取的数据</span>
                  <span className="data-info">{extractedData.length}个数据项</span>
                </div>
                <div className="extracted-data-list">
                  {extractedData.map((dataItem) => (
                    <div key={dataItem.id} className="extracted-item">
                      <div className="extracted-header">
                        <span className="extracted-name">{dataItem.name}</span>
                        <span className="extracted-time">{dataItem.timestamp}</span>
            <button
                          className="remove-btn"
                          onClick={() => setExtractedData(prev => prev.filter(d => d.id !== dataItem.id))}
                          title="删除数据项"
                        >
                          ✕
            </button>
          </div>
                      <div className="extracted-content">
                        <div className="extracted-description">{dataItem.description}</div>
                        <div className="extracted-details">
                          <div className="detail-row">
                            <span className="detail-label">选择器:</span>
                            <span className="detail-value" title={dataItem.selector}>
                              {dataItem.selector?.length > 50 
                                ? dataItem.selector.substring(0, 50) + '...' 
                                : dataItem.selector}
                            </span>
                </div>
                          <div className="detail-row">
                            <span className="detail-label">提取值:</span>
                            <span className="detail-value" title={dataItem.extractedValue}>
                              {dataItem.extractedValue?.length > 30 
                                ? dataItem.extractedValue.substring(0, 30) + '...' 
                                : dataItem.extractedValue}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">元素类型:</span>
                            <span className="detail-value">{dataItem.element.tagName}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )} */}
          </div>
        );

      case 'replay':
        return (
          <div className="tab-content">
            {/* 回放控制 */}
            <div className="control-group">
              <div className="group-header">
                <span className="group-title">🎬 回放控制</span>
                <span className="data-info">{jsonData ? jsonData.length : 0}字符</span>
                      </div>
                      
                      <div className="replay-mode-info">
                        <div className="mode-info-item">
                          <span className="mode-icon">▶️</span>
                          <span className="mode-name">JSON回放</span>
                          <span className="mode-desc">直接回放录制的JSON步骤</span>
                        </div>
                        <div className="mode-info-item">
                          <span className="mode-icon">🎯</span>
                          <span className="mode-name">脚本回放</span>
                          <span className="mode-desc">按配置的执行模式回放脚本</span>
                        </div>
                      </div>
              <div className="replay-main-controls">
                      <button
                  className={`replay-btn ${isReplaying ? 'danger' : 'success'}`}
                  onClick={isReplaying ? handleStopReplay : handleReplayJson}
                  disabled={!jsonData && !isReplaying}
                  title={isReplaying ? '停止回放' : '开始JSON回放'}
                >
                  {isReplaying ? (
                    <>
                      <span className="btn-icon">⏹️</span>
                      <span className="btn-text">停止回放</span>
                    </>
                  ) : (
                    <>
                      <span className="btn-icon">▶️</span>
                      <span className="btn-text">JSON回放</span>
                    </>
                  )}
                      </button>
                      
                      <button
                  className={`replay-btn ${isReplaying ? 'danger' : 'primary'}`}
                  onClick={isReplaying ? handleStopReplay : handleReplayScript}
                  disabled={isReplaying}
                  title={isReplaying ? '停止回放' : '开始脚本回放'}
                >
                  {isReplaying ? (
                    <>
                      <span className="btn-icon">⏹️</span>
                      <span className="btn-text">停止回放</span>
                    </>
                  ) : (
                    <>
                      <span className="btn-icon">🎯</span>
                      <span className="btn-text">脚本回放</span>
                    </>
                  )}
                      </button>
                    </div>
              
              {replayStatus && (
                <div className="replay-status-full">
                  <div className={`status-message ${isReplaying ? 'active' : 'completed'}`}>
                    {replayStatus}
                      </div>
                  {isReplaying && (
                    <div className="replay-progress">
                      <div className="progress-bar">
                        <div className="progress-fill"></div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {jsonData && (
                <div className="replay-data-info">
                  <div className="data-preview">
                    <div className="preview-header">📄 JSON数据预览</div>
                    <div className="preview-content">
                      <pre className="json-preview">
                        {jsonData.split('\n').slice(0, 5).map((line, index) => (
                          <div key={index} className="json-line">
                            {line.length > 80 ? line.substring(0, 80) + '...' : line}
                          </div>
                        ))}
                        {jsonData.split('\n').length > 5 && (
                          <div className="json-more">
                            ... 还有 {jsonData.split('\n').length - 5} 行
                          </div>
                        )}
                      </pre>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 录制动作列表 */}
            {recordedActions.length > 0 && (
              <div className="control-group">
                <div className="group-header">
                  <span className="group-title">📋 录制动作</span>
                  <span className="data-info">{recordedActions.length}个动作</span>
                </div>
                <div className="actions-list-full">
                  {recordedActions.map((action, index) => (
                    <div key={index} className="action-item-full">
                      <div className="action-index">{index + 1}</div>
                      <div className="action-content">
                        <div className="action-header">
                          <span className="action-type-full">{action.type}</span>
                          <span className="action-time-full">
                            {new Date(action.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="action-details-full">
                          {action.selector && (
                            <div className="action-detail-row">
                              <span className="detail-label">选择器:</span>
                              <span className="detail-value" title={action.selector}>
                                {action.selector.length > 60 
                                  ? action.selector.substring(0, 60) + '...' 
                                  : action.selector}
                              </span>
                            </div>
                          )}
                          {action.text && (
                            <div className="action-detail-row">
                              <span className="detail-label">文本:</span>
                              <span className="detail-value">{action.text}</span>
                            </div>
                          )}
                          {action.value && (
                            <div className="action-detail-row">
                              <span className="detail-label">值:</span>
                              <span className="detail-value">{action.value}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 'code':
        return (
          <div className="tab-content code-tab">
            <div className="code-header-full">
              <div className="code-title-full">📝 生成的代码</div>
              <div className="code-controls-full">
                <div className="format-tabs">
                      <button
                    className={`format-tab ${codeFormat === 'js' ? 'active' : ''}`}
                    onClick={() => setCodeFormat('js')}
                  >
                    JavaScript
                  </button>
                  <button
                    className={`format-tab ${codeFormat === 'json' ? 'active' : ''}`}
                    onClick={() => setCodeFormat('json')}
                    disabled={!jsonData}
                  >
                    JSON
                      </button>
                      <button
                    className={`format-tab ${codeFormat === 'script' ? 'active' : ''}`}
                    onClick={() => setCodeFormat('script')}
                  >
                    脚本
                      </button>
                    </div>
                <button
                  className="export-btn"
                  onClick={handleExportCode}
                  title="导出代码"
                >
                  📤 导出
                </button>
              </div>
            </div>

            {/* 脚本配置面板 */}
            {codeFormat === 'script' && (
              <div className="script-config-panel">
                <div className="script-config-content">
                  <div className="config-row-compact">
                    <div className="config-item">
                      <label className="config-label">脚本名称:</label>
                      <input
                        type="text"
                        className="config-input"
                        value={scriptConfig.name}
                        onChange={(e) => setScriptConfig({...scriptConfig, name: e.target.value})}
                        placeholder="请输入脚本名称"
                      />
                    </div>
                    <div className="config-item">
                      <label className="config-label">脚本类型:</label>
                      <select
                        className="config-select"
                        value={scriptConfig.type}
                        onChange={(e) => setScriptConfig({...scriptConfig, type: e.target.value})}
                      >
                        <option value="UI自动化">UI自动化</option>
                        <option value="接口测试">接口测试</option>
                        <option value="性能测试">性能测试</option>
                        <option value="回归测试">回归测试</option>
                      </select>
                    </div>
                    <div className="config-item">
                      <label className="config-label">执行模式:</label>
                      <select
                        className="config-select"
                        value={scriptConfig.executionMode}
                        onChange={(e) => setScriptConfig({...scriptConfig, executionMode: e.target.value})}
                      >
                        <option value="strict">严格执行模式</option>
                        <option value="monitoring">监听模式</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="code-content-full">
              <pre className="code-block-full">
                {getCurrentCodeContent()}
              </pre>
            </div>

            <div className="code-info">
              <div className="info-item">
                <span className="info-label">代码长度:</span>
                <span className="info-value">
                  {codeFormat === 'script' ? getCurrentCodeContent().length : codeLength} 字符
                </span>
              </div>
              <div className="info-item">
                <span className="info-label">最后更新:</span>
                <span className="info-value">{lastUpdate}</span>
              </div>
              <div className="info-item">
                <span className="info-label">格式:</span>
                <span className="info-value">
                  {codeFormat === 'js' ? 'JavaScript' : 
                   codeFormat === 'json' ? 'JSON' : '脚本'}
                </span>
              </div>
              {codeFormat === 'script' && (
                <>
                  <div className="info-item">
                    <span className="info-label">执行步骤:</span>
                    <span className="info-value">
                      {jsonData ? jsonData.split('\n').filter(line => line.trim()).length : 0} 个
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">检查点:</span>
                    <span className="info-value">
                      {(assertions || []).length + (loginCheckpoints || []).length} 个
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">输出变量:</span>
                    <span className="info-value">
                      {(networkVariables || []).length + (extractedData || []).length + Object.values(predefinedData).filter(data => data.value).length} 个
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        );

      case 'network':
        return (
          <div className="tab-content network-tab">
            <div className="network-layout">
              {/* 网络请求列表 */}
              <div className={selectedRequest ? 'network-requests-panel' : 'network-requests-panel-full'}>
                <div className="network-header">
                  <div className="network-title">📡 网络请求</div>
                  <div className="network-stats">
                    <span className="request-count">{networkRequests.length} 个请求</span>
                    <button className="clear-btn" onClick={() => setNetworkRequests([])}>
                      🗑️ 清空
                    </button>
                  </div>
                </div>

                <div className="requests-table">
                  <div className="table-header">
                    <div className="col-method">方法</div>
                    <div className="col-url">URL</div>
                    <div className="col-status">状态</div>
                    <div className="col-time">时间</div>
                    <div className="col-size">大小</div>
                  </div>
                  
                  <div className="table-body">
                    {networkRequests.map((request) => (
                      <div
                        key={request.id}
                        className={`request-row ${selectedRequest?.id === request.id ? 'selected' : ''}`}
                        onClick={() => handleRequestClick(request)}
                        onContextMenu={(e) => handleRequestRightClick(e, request)}
                      >
                        <div className={`col-method method-${request.method.toLowerCase()}`}>
                          {request.method}
                        </div>
                        <div className="col-url" title={request.url}>
                          {request.url.length > 60 
                            ? '...' + request.url.substring(request.url.length - 60)
                            : request.url}
                        </div>
                        <div className={`col-status status-${Math.floor(request.status / 100)}xx`}>
                          {request.status}
                        </div>
                        <div className="col-time">{request.time}</div>
                        <div className="col-size">{request.size}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 请求详情面板 */}
              {selectedRequest && (
                <div className="network-details-panel">
                  <div className="details-header">
                    <div className="details-title">
                      <span className={`method-badge method-${selectedRequest.method.toLowerCase()}`}>
                        {selectedRequest.method}
                      </span>
                      <span className="request-url">{selectedRequest.url}</span>
                      </div>
                      <button
                      className="close-details"
                      onClick={() => setSelectedRequest(null)}
                      >
                      ✕
                      </button>
                  </div>

                  <div className="details-content">
                    <div className="details-tabs">
                      <div className="detail-section">
                        <div className="section-title">📤 请求信息</div>
                        <div className="section-content">
                          <div className="info-grid">
                            <div className="info-item">
                              <span className="label">状态:</span>
                              <span className={`value status-${Math.floor(selectedRequest.status / 100)}xx`}>
                                {selectedRequest.status} {selectedRequest.statusText}
                              </span>
                            </div>
                            <div className="info-item">
                              <span className="label">时间:</span>
                              <span className="value">{selectedRequest.time}</span>
                            </div>
                            <div className="info-item">
                              <span className="label">大小:</span>
                              <span className="value">{selectedRequest.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {selectedRequest.requestBody && (
                        <div className="detail-section">
                          <div className="section-title">📝 请求体</div>
                          <div className="section-content">
                            <pre className="json-content">
                              {JSON.stringify(selectedRequest.requestBody, null, 2)}
                            </pre>
                    </div>
                  </div>
                )}

                      <div className="detail-section">
                        <div className="section-title">📥 响应数据</div>
                        <div className="section-content">
                          <div className="response-content">
                            {typeof selectedRequest.responseBody === 'object' ? (
                              <JsonViewer 
                                data={selectedRequest.responseBody}
                                onFieldRightClick={(fieldPath, value, event) => 
                                  handleResponseFieldRightClick(event, selectedRequest, fieldPath, value)
                                }
                              />
                            ) : (
                              <pre className="json-content">
                                {selectedRequest.responseBody}
                              </pre>
                            )}
                          </div>
                        </div>
                </div>

                      <div className="detail-section">
                        <div className="section-title">📋 请求头</div>
                        <div className="section-content">
                          <div className="headers-list">
                            {Object.entries(selectedRequest.requestHeaders).map(([key, value]) => (
                              <div key={key} className="header-item">
                                <span className="header-key">{key}:</span>
                                <span className="header-value">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="detail-section">
                        <div className="section-title">📋 响应头</div>
                        <div className="section-content">
                          <div className="headers-list">
                            {Object.entries(selectedRequest.responseHeaders).map(([key, value]) => (
                              <div key={key} className="header-item">
                                <span className="header-key">{key}:</span>
                                <span className="header-value">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 已保存的变量和检查点 */}
            {(networkVariables.length > 0 || loginCheckpoints.length > 0) && (
              <div className="network-summary">
                {loginCheckpoints.length > 0 && (
                  <div className="summary-section">
                    <div className="summary-title">🎯 登录检查点</div>
                    <div className="checkpoints-list">
                      {loginCheckpoints.map((checkpoint) => (
                        <div key={checkpoint.id} className="checkpoint-item">
                          <span className="checkpoint-name">{checkpoint.name}</span>
                          <span className="checkpoint-url">{checkpoint.method} {checkpoint.url}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {networkVariables.length > 0 && (
                  <div className="summary-section">
                    <div className="summary-title">💾 提取的变量</div>
                    <div className="variables-list">
                      {networkVariables.map((variable) => (
                        <div key={variable.id} className="variable-item">
                          <span className="variable-name">{variable.name}</span>
                          <span className="variable-path">{variable.fieldPath}</span>
                          <span className="variable-value" title={JSON.stringify(variable.value)}>
                            {typeof variable.value === 'string' 
                              ? variable.value.length > 20 
                                ? variable.value.substring(0, 20) + '...'
                                : variable.value
                              : JSON.stringify(variable.value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 右键菜单 */}
            {contextMenu && (
              <div 
                className="context-menu"
                style={{ 
                  position: 'fixed', 
                  left: contextMenu.x, 
                  top: contextMenu.y,
                  zIndex: 1000
                }}
                onClick={() => setContextMenu(null)}
              >
                {contextMenu.type === 'request' && (
                  <div className="menu-items">
                    <div 
                      className="menu-item"
                      onClick={() => setAsLoginCheckpoint(contextMenu.request)}
                    >
                      🎯 设为登录检查点
                </div>
                  </div>
                )}
                {contextMenu.type === 'responseField' && (
                  <div className="menu-items">
                    <div 
                      className="menu-item"
                      onClick={() => saveAsVariable(contextMenu.request, contextMenu.fieldPath, contextMenu.value)}
                    >
                      💾 保存为变量
              </div>
            </div>
          )}
              </div>
            )}
          </div>
        );

      case 'output':
        return (
          <div className="tab-content output-tab">
            <div className="output-header">
              <div className="output-title">📤 脚本输出</div>
              <div className="output-stats">
                <span className="output-item">
                  断言: <span className="output-count">{assertions.length}</span>
                </span>
                <span className="output-item">
                  登录: <span className="output-count">{loginCheckpoints.length}</span>
                </span>
                <span className="output-item">
                  预定义: <span className="output-count">{Object.values(predefinedData).filter(data => data.value).length}</span>
                </span>
                <span className="output-item">
                  自定义: <span className="output-count">{extractedData.length}</span>
                </span>
                <span className="output-item">
                  网络: <span className="output-count">{networkVariables.length}</span>
                </span>
              </div>
            </div>

            {/* 断言检查点列表 */}
            <div className="output-section">
              <div className="output-section-header">
                <div className="section-title">🎯 断言检查点</div>
                <span className="section-count">{assertions.length}个</span>
              </div>
              {assertions.length > 0 ? (
                <div className="assertions-list">
                  {assertions.map((assertion) => (
                    <div key={assertion.id} className="assertion-item">
                      <div className="assertion-header">
                        <div className="assertion-type-info">
                          <span className={`assertion-type assertion-${assertion.type}`}>
                            {assertion.type === 'visibility' ? '👁️ 可见性' : 
                             assertion.type === 'text' ? '📝 文本' : 
                             assertion.type === 'value' ? '💎 值' : assertion.type}
                          </span>
                          <span className="assertion-name">{assertion.name}</span>
                        </div>
                        <div className="assertion-actions">
                          <span className="assertion-time">{assertion.timestamp}</span>
                          <button 
                            className="remove-btn"
                            onClick={() => setAssertions(prev => prev.filter(a => a.id !== assertion.id))}
                            title="删除断言"
                          >
                            ✕
                          </button>
                        </div>
                      </div>
                      <div className="assertion-content">
                        <div className="assertion-description">{assertion.description}</div>
                        <div className="assertion-details">
                          <div className="detail-row">
                            <span className="detail-label">选择器:</span>
                            <span className="detail-value" title={assertion.selector}>
                              {assertion.selector?.length > 50 
                                ? assertion.selector.substring(0, 50) + '...' 
                                : assertion.selector}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">预期值:</span>
                            <span className="detail-value" title={assertion.expectedValue}>
                              {assertion.expectedValue?.length > 30 
                                ? assertion.expectedValue.substring(0, 30) + '...' 
                                : assertion.expectedValue}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">类型:</span>
                            <span className="detail-value">{assertion.type}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="output-empty-section">
                  <div className="empty-icon">🎯</div>
                  <div className="empty-text">还没有断言检查点</div>
                  <div className="empty-hint">点击控制页面的断言按钮创建断言检查点</div>
                </div>
              )}
              </div>

            {/* 登录检查点列表 */}
            {loginCheckpoints.length > 0 && (
              <div className="output-section">
                <div className="output-section-header">
                  <div className="section-title">🔐 登录检查点</div>
                  <span className="section-count">{loginCheckpoints.length}个</span>
            </div>
                <div className="login-checkpoints-list">
                  {loginCheckpoints.map((checkpoint) => (
                    <div key={checkpoint.id} className="checkpoint-item">
                      <div className="checkpoint-header">
                        <div className="checkpoint-type-info">
                          <span className={`checkpoint-type checkpoint-${checkpoint.type}`}>
                            🔐 {checkpoint.type === 'visibility' ? '可见性检查' : 
                                 checkpoint.type === 'text' ? '文本检查' : 
                                 checkpoint.type === 'value' ? '值检查' : checkpoint.type}
                          </span>
                          <span className="checkpoint-name">{checkpoint.name}</span>
                        </div>
                        <div className="checkpoint-actions">
                          <span className="checkpoint-time">{checkpoint.timestamp}</span>
              <button
                            className="remove-btn"
                            onClick={() => setLoginCheckpoints(prev => prev.filter(c => c.id !== checkpoint.id))}
                            title="删除检查点"
                          >
                            ✕
              </button>
                        </div>
                      </div>
                      <div className="checkpoint-content">
                        <div className="checkpoint-description">{checkpoint.description}</div>
                        <div className="checkpoint-details">
                          <div className="detail-row">
                            <span className="detail-label">选择器:</span>
                            <span className="detail-value" title={checkpoint.selector}>
                              {checkpoint.selector?.length > 50 
                                ? checkpoint.selector.substring(0, 50) + '...' 
                                : checkpoint.selector}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">预期值:</span>
                            <span className="detail-value" title={checkpoint.expectedValue}>
                              {checkpoint.expectedValue?.length > 30 
                                ? checkpoint.expectedValue.substring(0, 30) + '...' 
                                : checkpoint.expectedValue}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">类型:</span>
                            <span className="detail-value">{checkpoint.type}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                </div>
              )}

            {/* 预定义数据汇总 */}
            {Object.values(predefinedData).some(data => data.value) && (
              <div className="output-section">
                <div className="output-section-header">
                  <div className="section-title">🎯 预定义数据</div>
                  <span className="section-count">
                    {Object.values(predefinedData).filter(data => data.value).length}/3 已填充
                  </span>
            </div>
                <div className="predefined-summary">
                  {Object.entries(predefinedData).map(([key, data]) => (
                    data.value && (
                      <div key={key} className="predefined-summary-item">
                        <div className="summary-header">
                          <span className="summary-name">{data.name}</span>
                          <span className="summary-time">{data.timestamp}</span>
          </div>
                        <div className="summary-content">
                          <div className="summary-value" title={data.value}>
                            {data.value}
                          </div>
                          <div className="summary-selector" title={data.selector}>
                            选择器: {data.selector?.length > 40 
                              ? data.selector.substring(0, 40) + '...' 
                              : data.selector}
                          </div>
                        </div>
                      </div>
                    )
                  ))}
                </div>
              </div>
            )}

            {/* 自定义提取数据列表 */}
            {extractedData.length > 0 && (
              <div className="output-section">
                <div className="output-section-header">
                  <div className="section-title">📋 自定义提取数据</div>
                  <span className="section-count">{extractedData.length}个</span>
                </div>
                <div className="extracted-data-list">
                  {extractedData.map((dataItem) => (
                    <div key={dataItem.id} className="extracted-item">
                      <div className="extracted-header">
                        <span className="extracted-name">{dataItem.name}</span>
                        <span className="extracted-time">{dataItem.timestamp}</span>
            <button
                          className="remove-btn"
                          onClick={() => setExtractedData(prev => prev.filter(d => d.id !== dataItem.id))}
                          title="删除数据项"
                        >
                          ✕
            </button>
          </div>
                      <div className="extracted-content">
                        <div className="extracted-description">{dataItem.description}</div>
                        <div className="extracted-details">
                          <div className="detail-row">
                            <span className="detail-label">选择器:</span>
                            <span className="detail-value" title={dataItem.selector}>
                              {dataItem.selector?.length > 50 
                                ? dataItem.selector.substring(0, 50) + '...' 
                                : dataItem.selector}
                            </span>
        </div>
                          <div className="detail-row">
                            <span className="detail-label">提取值:</span>
                            <span className="detail-value" title={dataItem.extractedValue}>
                              {dataItem.extractedValue?.length > 30 
                                ? dataItem.extractedValue.substring(0, 30) + '...' 
                                : dataItem.extractedValue}
                            </span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">元素类型:</span>
                            <span className="detail-value">{dataItem.element.tagName}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 网络变量列表 */}
            {networkVariables.length > 0 && (
              <div className="output-section">
                <div className="output-section-header">
                  <div className="section-title">💾 网络变量</div>
                  <span className="section-count">{networkVariables.length}个</span>
                </div>
                <div className="network-variables-list">
                  {networkVariables.map((variable) => (
                    <div key={variable.id} className="network-variable-item">
                      <div className="variable-header">
                        <span className="variable-name">{variable.name}</span>
                        <span className="variable-type">{variable.type}</span>
                      </div>
                      <div className="variable-content">
                        <div className="variable-description">{variable.description}</div>
                        <div className="variable-details">
                          <div className="detail-row">
                            <span className="detail-label">来源:</span>
                            <span className="detail-value">{variable.source}</span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">路径:</span>
                            <span className="detail-value">{variable.fieldPath}</span>
                          </div>
                          <div className="detail-row">
                            <span className="detail-label">值:</span>
                            <span className="detail-value" title={JSON.stringify(variable.value)}>
                              {typeof variable.value === 'string' 
                                ? variable.value.length > 30 
                                  ? variable.value.substring(0, 30) + '...'
                                  : variable.value
                                : JSON.stringify(variable.value)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 空状态提示 */}
            {assertions.length === 0 && 
             loginCheckpoints.length === 0 && 
             Object.values(predefinedData).every(data => !data.value) && 
             extractedData.length === 0 && 
             networkVariables.length === 0 && (
              <div className="output-empty">
                <div className="empty-icon">📤</div>
                <div className="empty-title">暂无输出内容</div>
                <div className="empty-description">
                  开始录制和提取数据后，这里会显示：
                </div>
                <div className="empty-list">
                  <div className="empty-item">🎯 断言检查点</div>
                  <div className="empty-item">📋 提取的数据</div>
                  <div className="empty-item">💾 网络变量</div>
                </div>

                <div className="empty-list">
                  <div className="empty-item">🎯 断言检查点</div>
                  <div className="empty-item">🔐 登录检查点</div>
                  <div className="empty-item">📋 提取的数据</div>
                  <div className="empty-item">💾 网络变量</div>
                </div>

                <div className="empty-list">
                  <div className="empty-item">🎯 断言检查点</div>
                  <div className="empty-item">🔐 登录检查点</div>
                  <div className="empty-item">📋 提取的数据</div>
                  <div className="empty-item">💾 网络变量</div>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return <div>未知的Tab</div>;
    }
  };

  const isRecord = recorderState.mode === 'recording' || recorderState.mode === 'recording-inspecting';

  return (
    <div className="app">
      <header className="header">
        <div className="header-left">
          <h1>🎬 录制器</h1>
          <div className="status-compact">
            <div className={`status-dot ${isInspectMode ? 'inspect' : (isRecording ? 'recording' : 'paused')}`}></div>
            <span className="status-text">
              {isInspectMode ? '🔍' : (isRecording ? '📹' : '⏸️')}
            </span>
            <span className="code-length">{codeLength}字符</span>
          </div>
          <div className="status-details">
            <span className={`mode-badge-header mode-${recorderState.mode}`}>
              {recorderState.mode === 'recording' ? '📹 录制中' :
               recorderState.mode === 'inspecting' ? '🔍 检查中' :
               recorderState.mode === 'recording-inspecting' ? '📹🔍 录制+检查' :
               recorderState.mode === 'standby' ? '⏸️ 待机' :
               recorderState.mode === 'none' ? '⏹️ 停止' : '🔄 未知'}
            </span>
            <span className="language-badge-header">{recorderState.language || 'JS'}</span>
            {recordedActions.length > 0 && (
              <span className="actions-count-badge">{recordedActions.length}个动作</span>
            )}
          </div>
        </div>
        <div className="header-right">
          <span className="last-update">更新: {lastUpdate}</span>
          {recorderState.actionSelector && (
            <div className="current-selector-header" title={recorderState.actionSelector}>
              选择器: {recorderState.actionSelector.length > 20
                ? recorderState.actionSelector.substring(0, 20) + '...'
                : recorderState.actionSelector}
            </div>
          )}
        </div>
      </header>

      <div className="main-layout">
        {/* Tab导航栏 */}
        <div className="tab-navigation">
            <button
            className={`tab-btn ${activeTab === 'control' ? 'active' : ''}`}
            onClick={() => setActiveTab('control')}
          >
            <span className="tab-icon">🎛️</span>
            <span className="tab-label">控制</span>
          </button>
          <button
            className={`tab-btn ${activeTab === 'replay' ? 'active' : ''}`}
            onClick={() => setActiveTab('replay')}
          >
            <span className="tab-icon">🎬</span>
            <span className="tab-label">回放</span>
            {jsonData && <span className="tab-badge">{Math.ceil(jsonData.length / 1000)}K</span>}
          </button>
          <button
            className={`tab-btn ${activeTab === 'code' ? 'active' : ''}`}
            onClick={() => setActiveTab('code')}
          >
            <span className="tab-icon">📝</span>
            <span className="tab-label">代码</span>
            {codeLength > 0 && <span className="tab-badge">{Math.ceil(codeLength / 1000)}K</span>}
          </button>
          <button
            className={`tab-btn ${activeTab === 'network' ? 'active' : ''}`}
            onClick={() => setActiveTab('network')}
          >
            <span className="tab-icon">🌐</span>
            <span className="tab-label">网络</span>
            {networkRequests.length > 0 && <span className="tab-badge">{networkRequests.length}</span>}
          </button>
          <button
            className={`tab-btn ${activeTab === 'output' ? 'active' : ''}`}
            onClick={() => setActiveTab('output')}
          >
            <span className="tab-icon">📤</span>
            <span className="tab-label">输出</span>
            {(assertions.length > 0 || Object.values(predefinedData).some(data => data.value) || extractedData.length > 0 || networkVariables.length > 0) && (
              <span className="tab-badge">
                {assertions.length + Object.values(predefinedData).filter(data => data.value).length + extractedData.length + networkVariables.length}
              </span>
            )}
            </button>
          </div>

        {/* Tab内容区域 */}
        <div className="tab-content-area">
          {renderTabContent()}
            </div>
          </div>

      {/* 选中元素弹窗 */}
      {showElementModal && selectedElement && (
        <div className="element-modal-overlay">
          <div className="element-modal">
            <div className="modal-header">
              <div className="modal-title">🎯 选中元素</div>
              <button className="modal-close" onClick={closeElementModal}>✕</button>
            </div>
            
            <div className="modal-content">
              <div className="element-info-modal">
                <div className="element-basic-modal">
                  <div className="element-row">
                    <span className="element-label">标签:</span>
                    <span className="element-value">{selectedElement.tagName || '-'}</span>
                  </div>
                  <div className="element-row">
                    <span className="element-label">文本:</span>
                    <span className="element-value" title={selectedElement.textContent || selectedElement.innerText}>
                      {(selectedElement.textContent || selectedElement.innerText || '-').length > 40
                        ? (selectedElement.textContent || selectedElement.innerText || '-').substring(0, 40) + '...'
                        : (selectedElement.textContent || selectedElement.innerText || '-')}
                    </span>
                  </div>
                  {selectedElement.id && (
                    <div className="element-row">
                      <span className="element-label">ID:</span>
                      <span className="element-value">{selectedElement.id}</span>
                    </div>
                  )}
                  {selectedElement.className && (
                    <div className="element-row">
                      <span className="element-label">类:</span>
                      <span className="element-value" title={selectedElement.className}>
                        {selectedElement.className.length > 40
                          ? selectedElement.className.substring(0, 40) + '...'
                          : selectedElement.className}
                      </span>
                    </div>
                  )}
                  {selectedElement.position && (
                    <>
                      <div className="element-row">
                        <span className="element-label">位置:</span>
                        <span className="element-value">
                          ({Math.round(selectedElement.position.x)}, {Math.round(selectedElement.position.y)})
                        </span>
                      </div>
                      <div className="element-row">
                        <span className="element-label">尺寸:</span>
                        <span className="element-value">
                          {Math.round(selectedElement.position.width)} × {Math.round(selectedElement.position.height)}
                        </span>
                      </div>
                        </>
                      )}
                  {selectedElement.value && (
                    <div className="element-row">
                      <span className="element-label">值:</span>
                      <span className="element-value" title={selectedElement.value}>
                        {selectedElement.value.length > 40
                          ? selectedElement.value.substring(0, 40) + '...'
                          : selectedElement.value}
                      </span>
                    </div>
                  )}
                  {selectedElement.href && (
                    <div className="element-row">
                      <span className="element-label">链接:</span>
                      <span className="element-value" title={selectedElement.href}>
                        {selectedElement.href.length > 40
                          ? selectedElement.href.substring(0, 40) + '...'
                          : selectedElement.href}
                      </span>
                    </div>
                  )}
                  </div>

                {selectedElement.selectors && (
                  <div className="selectors-modal">
                    <div className="selector-tabs">
                      <button 
                        className={`selector-tab ${activeSelectorType === 'playwright' ? 'active' : ''} ${selectedElement.selectors?.playwright ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('playwright')}
                        title={selectedElement.selectors?.playwright ? "Playwright选择器（可用）" : "Playwright选择器（未生成）"}
                      >
                        PW {selectedElement.selectors?.playwright ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'css' ? 'active' : ''} ${selectedElement.selectors?.css ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('css')}
                        title={selectedElement.selectors?.css ? "CSS选择器（可用）" : "CSS选择器（未生成）"}
                      >
                        CSS {selectedElement.selectors?.css ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'xpath' ? 'active' : ''} ${selectedElement.selectors?.xpath ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('xpath')}
                        title={selectedElement.selectors?.xpath ? "XPath选择器（可用）" : "XPath选择器（未生成）"}
                      >
                        XP {selectedElement.selectors?.xpath ? '✓' : '×'}
                      </button>
                    </div>
                    <div className="selector-content">
                      <div className="selector-display">
                        <span className="selector-text" title={selectedElement.selectors[activeSelectorType]}>
                          {(() => {
                            const selector = selectedElement.selectors[activeSelectorType];
                            if (!selector) return '未生成';
                            return selector.length > 60 ? selector.substring(0, 60) + '...' : selector;
                          })()}
                        </span>
                        <button
                          className="copy-btn"
                          onClick={() => {
                            const selector = selectedElement.selectors[activeSelectorType];
                            if (selector) {
                              navigator.clipboard.writeText(selector);
                            }
                          }}
                          title="复制选择器"
                          disabled={!selectedElement.selectors[activeSelectorType]}
                        >
                          📋
                        </button>
                      </div>
              </div>
            </div>
          )}

                {/* 命名和保存区域 */}
                <div className="element-save-section">
                  <div className="save-input-group">
                    <input
                      type="text"
                      value={elementName}
                      onChange={(e) => setElementName(e.target.value)}
                      placeholder={
                        window.currentAction === 'extractPredefinedData' 
                          ? `${predefinedData[window.currentDataType]?.name || '变量名'} (自动填充)`
                          : `${selectedElement.tagName}_${selectedElement.id || 'element'}`
                      }
                      className="element-name-input"
                      disabled={window.currentAction === 'extractPredefinedData'}
                      readOnly={window.currentAction === 'extractPredefinedData'}
                    />
                    <button
                      className="save-element-btn"
                      onClick={saveSelectedElement}
                    >
                      💾 保存
                    </button>
        </div>
      </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 断言弹窗 */}
      {assertionModal && (
        <div className="assertion-modal-overlay">
          <div className="assertion-modal">
            <div className="modal-header">
              <div className="modal-title">
                {assertionModal.icon} {assertionModal.name}
              </div>
              <button className="modal-close" onClick={closeAssertionModal}>✕</button>
            </div>
            <div className="modal-content">
              <div className="assertion-info">
                <div className="assertion-type">
                  <span className="assertion-type-icon">{assertionModal.icon}</span>
                  <span className="assertion-type-name">{assertionModal.name}</span>
                </div>

                {/* 如果有多个选择器，显示选择器选择功能 */}
                {assertionModal.fromRecord && assertionModal.selectors ? (
                  <div className="assertion-selectors">
                    <div className="selector-label">选择器:</div>
                    <div className="selector-tabs">
                      <button 
                        className={`selector-tab ${activeSelectorType === 'playwright' ? 'active' : ''} ${assertionModal.selectors?.playwright ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('playwright')}
                        title={assertionModal.selectors?.playwright ? "Playwright选择器（可用）" : "Playwright选择器（未生成）"}
                      >
                        PW {assertionModal.selectors?.playwright ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'css' ? 'active' : ''} ${assertionModal.selectors?.css ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('css')}
                        title={assertionModal.selectors?.css ? "CSS选择器（可用）" : "CSS选择器（未生成）"}
                      >
                        CSS {assertionModal.selectors?.css ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'xpath' ? 'active' : ''} ${assertionModal.selectors?.xpath ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('xpath')}
                        title={assertionModal.selectors?.xpath ? "XPath选择器（可用）" : "XPath选择器（未生成）"}
                      >
                        XP {assertionModal.selectors?.xpath ? '✓' : '×'}
                      </button>
                    </div>
                    <div className="selector-content">
                      <div className="selector-display">
                        <span className="selector-text" title={assertionModal.selectors[activeSelectorType]}>
                          {(() => {
                            const selector = assertionModal.selectors[activeSelectorType];
                            if (!selector) return '未生成';
                            return selector.length > 50 ? selector.substring(0, 50) + '...' : selector;
                          })()}
                        </span>
                        <button
                          className="copy-btn"
                          onClick={() => {
                            const selector = assertionModal.selectors[activeSelectorType];
                            if (selector) {
                              navigator.clipboard.writeText(selector);
                            }
                          }}
                          title="复制选择器"
                          disabled={!assertionModal.selectors[activeSelectorType]}
                        >
                          📋
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="assertion-selector">
                    <span className="selector-label">选择器:</span>
                    <span className="selector-value" title={assertionModal.selector}>
                      {assertionModal.selector || '未指定'}
                    </span>
                  </div>
                )}

                <div className="assertion-expected">
                  <span className="expected-label">预期值:</span>
                  <span className="expected-value" title={assertionModal.expectedValue}>
                    {assertionModal.expectedValue}
                  </span>
                </div>
                <div className="assertion-description">
                  <span className="description-label">描述:</span>
                  <span className="description-value">{assertionModal.description}</span>
                </div>
              </div>
              <div className="assertion-actions">
                <button className="assertion-save-btn" onClick={saveAssertionData}>
                  💾 保存断言
                </button>
                <button className="assertion-cancel-btn" onClick={closeAssertionModal}>
                  ❌ 取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 登录检查点弹窗 */}
      {loginCheckpointModal && (
        <div className="checkpoint-modal-overlay">
          <div className="checkpoint-modal">
            <div className="modal-header">
              <div className="modal-title">
                {loginCheckpointModal.icon} {loginCheckpointModal.name}
              </div>
              <button className="modal-close" onClick={closeLoginCheckpointModal}>✕</button>
            </div>
            <div className="modal-content">
              <div className="checkpoint-info">
                <div className="checkpoint-type">
                  <span className="checkpoint-type-icon">{loginCheckpointModal.icon}</span>
                  <span className="checkpoint-type-name">{loginCheckpointModal.name}</span>
                </div>

                {/* 如果有多个选择器，显示选择器选择功能 */}
                {loginCheckpointModal.fromRecord && loginCheckpointModal.selectors ? (
                  <div className="checkpoint-selectors">
                    <div className="selector-label">选择器:</div>
                    <div className="selector-tabs">
                      <button 
                        className={`selector-tab ${activeSelectorType === 'playwright' ? 'active' : ''} ${loginCheckpointModal.selectors?.playwright ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('playwright')}
                        title={loginCheckpointModal.selectors?.playwright ? "Playwright选择器（可用）" : "Playwright选择器（未生成）"}
                      >
                        PW {loginCheckpointModal.selectors?.playwright ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'css' ? 'active' : ''} ${loginCheckpointModal.selectors?.css ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('css')}
                        title={loginCheckpointModal.selectors?.css ? "CSS选择器（可用）" : "CSS选择器（未生成）"}
                      >
                        CSS {loginCheckpointModal.selectors?.css ? '✓' : '×'}
                      </button>
                      <button 
                        className={`selector-tab ${activeSelectorType === 'xpath' ? 'active' : ''} ${loginCheckpointModal.selectors?.xpath ? 'has-data' : ''}`}
                        onClick={() => setActiveSelectorType('xpath')}
                        title={loginCheckpointModal.selectors?.xpath ? "XPath选择器（可用）" : "XPath选择器（未生成）"}
                      >
                        XP {loginCheckpointModal.selectors?.xpath ? '✓' : '×'}
                      </button>
                    </div>
                    <div className="selector-content">
                      <div className="selector-display">
                        <span className="selector-text" title={loginCheckpointModal.selectors[activeSelectorType]}>
                          {(() => {
                            const selector = loginCheckpointModal.selectors[activeSelectorType];
                            if (!selector) return '未生成';
                            return selector.length > 50 ? selector.substring(0, 50) + '...' : selector;
                          })()}
                        </span>
                        <button
                          className="copy-btn"
                          onClick={() => {
                            const selector = loginCheckpointModal.selectors[activeSelectorType];
                            if (selector) {
                              navigator.clipboard.writeText(selector);
                            }
                          }}
                          title="复制选择器"
                          disabled={!loginCheckpointModal.selectors[activeSelectorType]}
                        >
                          📋
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="checkpoint-selector">
                    <span className="selector-label">选择器:</span>
                    <span className="selector-value" title={loginCheckpointModal.selector}>
                      {loginCheckpointModal.selector || '未指定'}
                    </span>
                  </div>
                )}

                <div className="checkpoint-expected">
                  <span className="expected-label">预期值:</span>
                  <span className="expected-value" title={loginCheckpointModal.expectedValue}>
                    {loginCheckpointModal.expectedValue}
                  </span>
                </div>
                <div className="checkpoint-description">
                  <span className="description-label">描述:</span>
                  <span className="description-value">{loginCheckpointModal.description}</span>
                </div>
              </div>
              <div className="checkpoint-actions">
                <button className="checkpoint-save-btn" onClick={saveLoginCheckpointData}>
                  💾 保存检查点
                </button>
                <button className="checkpoint-cancel-btn" onClick={closeLoginCheckpointModal}>
                  ❌ 取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
