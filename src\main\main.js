/**
 * Electron + Playwright 录制器 (v3.0 - 高度模块化版本)
 * 精简的主协调器，所有功能都委托给专门的管理器
 *
 * <AUTHOR> Agent
 * @version 3.0.0
 */

// 设置环境变量以激活 Playwright patch（确保在所有启动方式下都能激活）
if (!process.env.PLAYWRIGHT_ELECTRON_BRIDGE) {
  process.env.PLAYWRIGHT_ELECTRON_BRIDGE = 'true';
  console.log('🌉 已设置 PLAYWRIGHT_ELECTRON_BRIDGE 环境变量，激活 patch 功能');
}

// 导入核心配置
const {
  APP_CONFIG,
  IPC_EVENTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} = require('./constants');

// 导入管理器模块
const ElectronAppManager = require('./electron-app-manager');
const PlaywrightManager = require('./playwright-manager');
const IPCManager = require('./ipc-manager');
const MessageHandler = require('./message-handler');
const RecorderActionHandler = require('./recorder-action-handler');
const CleanupManager = require('./cleanup-manager');

/**
 * Electron Playwright 录制器主类 (v3.0)
 * 高度模块化的协调器，管理各个专门的管理器组件
 */
class ElectronPlaywrightRecorder {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    this.options = this._mergeOptions(options);
    this._initializeProperties();
    this._initializeManagers();
    process.env.PW_CODEGEN_NO_INSPECTOR = '1';
  }

  /**
   * 合并配置选项
   * @private
   */
  _mergeOptions(options) {
    return {
      language: APP_CONFIG.DEFAULT_LANGUAGE,
      outputFile: APP_CONFIG.DEFAULT_OUTPUT_FILE,
      windowTitle: APP_CONFIG.DEFAULT_WINDOW_TITLE,
      controlPanelTitle: APP_CONFIG.DEFAULT_CONTROL_PANEL_TITLE,
      ...options
    };
  }

  /**
   * 初始化基础属性
   * @private
   */
  _initializeProperties() {
    // 核心组件 (由管理器管理)
    this.browser = null;
    this.context = null;
    this.page = null;
    this.mainWindow = null;
    
    // 状态
    this.isRecording = false;
    this.generatedCode = '';
    this.selectedElement = null;
  }

  /**
   * 初始化管理器实例
   * @private
   */
  _initializeManagers() {
    // 核心管理器
    this.electronAppManager = new ElectronAppManager(this);
    this.playwrightManager = new PlaywrightManager(this);
    this.cleanupManager = new CleanupManager(this);
    
    // 功能管理器 (延迟初始化)
    this.ipcManager = null;
    this.messageHandler = null;
    this.actionHandler = null;
  }

  /**
   * 启动整个系统
   * @returns {Promise<ElectronPlaywrightRecorder>} 录制器实例
   */
  async start() {
    try {
      console.log('🚀 启动 Electron 控制的录制器...');

      // 设置全局变量，让 Playwright 补丁可以访问
      global.electronPlaywrightRecorder = this;

      // 首先初始化 IPC 管理器（在创建窗口之前）
      this._initializeIPCManager();

      // 初始化功能管理器（在 PlaywrightManager 之前）
      this._initializeFunctionalManagers();

      // 按顺序初始化各个管理器
      await this.electronAppManager.initialize();
      await this.playwrightManager.initialize();

      this._logStartupSuccess();
      return this;

    } catch (error) {
      console.error(ERROR_MESSAGES.SYSTEM_START_FAILED, error);
      await this.stop();
      throw error;
    }
  }

  /**
   * 停止系统
   */
  async stop() {
    try {
      await this.cleanupManager.cleanup();
    } catch (error) {
      console.error('❌ 停止系统时出错:', error);
    }
  }

  /**
   * 初始化 IPC 管理器（在创建窗口之前）
   * @private
   */
  _initializeIPCManager() {
    // 初始化 IPC 管理器
    this.ipcManager = new IPCManager(this);
    this.ipcManager.setupHandlers();
    console.log(SUCCESS_MESSAGES.IPC_SETUP);
  }

  /**
   * 初始化功能管理器
   * @private
   */
  _initializeFunctionalManagers() {
    // 初始化消息处理器
    this.messageHandler = new MessageHandler(this);

    // 初始化动作处理器
    this.actionHandler = new RecorderActionHandler(this);

    // 通信管理现在由 PlaywrightManager 在 context 级别统一处理
    console.log(SUCCESS_MESSAGES.COMMUNICATION_ESTABLISHED);
  }

  /**
   * 处理录制器控制命令
   * @param {string} action - 控制动作
   * @param {Object} data - 控制数据
   * @returns {Promise<Object>} 控制结果
   */
  async handleRecorderControl(action, data) {
    try {
      console.log(`🎛️ 执行录制器控制: ${action}`, data);

      const result = await this.actionHandler.executeAction(action, data);

      return { success: true, result };

    } catch (error) {
      console.error(`${ERROR_MESSAGES.RECORDER_CONTROL_FAILED} (${action}):`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 发送消息到渲染进程
   * @param {string} channel - 消息通道
   * @param {any} data - 消息数据
   */
  sendToRenderer(channel, data) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // ==================== 辅助方法 ====================


  /**
   * 记录启动成功信息
   * @private
   */
  _logStartupSuccess() {
    console.log(SUCCESS_MESSAGES.SYSTEM_STARTED);
    console.log('🎛️ 控制面板窗口已打开');
    console.log('🎬 录制器窗口已准备就绪');
    console.log('\n📋 使用说明:');
    console.log('1. 🎛️ Electron 控制面板窗口已打开');
    console.log('2. 🎬 Playwright 录制器窗口已准备就绪');
    console.log('3. 两个窗口通过 CDP 协议通信');
    console.log('4. 在控制面板中可以:');
    console.log('   - 暂停/继续录制');
    console.log('   - 切换检查模式');
    console.log('   - 导航到新页面');
    console.log('   - 实时查看生成的代码');
    console.log('   - 导出代码到剪贴板');
    console.log('\n🎯 请在控制面板中输入 URL 并导航，然后开始录制操作');
  }
}

// 应用程序实例
const recorder = new ElectronPlaywrightRecorder({
  language: APP_CONFIG.DEFAULT_LANGUAGE,
  outputFile: APP_CONFIG.DEFAULT_OUTPUT_FILE,
  windowTitle: APP_CONFIG.DEFAULT_WINDOW_TITLE,
  controlPanelTitle: APP_CONFIG.DEFAULT_CONTROL_PANEL_TITLE
});

/**
 * 启动应用
 */
async function startApp() {
  try {
    await recorder.start();
    console.log('✅ 系统启动成功！');
  } catch (error) {
    console.error(ERROR_MESSAGES.SYSTEM_START_FAILED, error);
    process.exit(1);
  }
}

/**
 * 检查是否安装了 Electron
 */
function checkElectronInstallation() {
  try {
    require('electron');
    return true;
  } catch (error) {
    console.error(ERROR_MESSAGES.ELECTRON_NOT_FOUND);
    return false;
  }
}

// 启动应用
if (checkElectronInstallation()) {
  startApp().catch(console.error);
}

module.exports = ElectronPlaywrightRecorder;
