/**
 * 最终代码生成拦截测试
 * 验证修复后的代码生成拦截功能
 */

// 设置环境变量
process.env.PLAYWRIGHT_ELECTRON_BRIDGE = 'true';

async function finalCodeGenerationTest() {
  console.log('🎯 最终代码生成拦截测试...');
  
  try {
    // 1. 加载playwright-core
    console.log('📦 加载playwright-core...');
    require('playwright-core');
    
    // 等待patch应用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 验证patch状态
    console.log('✅ Patch状态验证:');
    console.log('  playwrightReplayAPI:', !!global.playwrightReplayAPI);
    console.log('  playwrightElectronBridge:', !!global.playwrightElectronBridge);
    console.log('  originalSetSources:', !!global.playwrightElectronBridge?.originalSetSources);
    
    // 3. 检查文件修改状态
    console.log('📋 文件修改状态:');
    
    const fs = require('fs');
    const path = require('path');
    
    // 检查recorder.js
    const recorderPath = path.join('node_modules', 'playwright-core', 'lib', 'server', 'recorder.js');
    const recorderContent = fs.readFileSync(recorderPath, 'utf-8');
    const hasRecorderPatch = recorderContent.includes('ELECTRON_BRIDGE_PATCH_APPLIED');
    console.log('  recorder.js patch:', hasRecorderPatch ? '✅' : '❌');
    
    // 检查recorderApp.js
    const recorderAppPath = path.join('node_modules', 'playwright-core', 'lib', 'server', 'recorder', 'recorderApp.js');
    const recorderAppContent = fs.readFileSync(recorderAppPath, 'utf-8');
    const hasAppIntercept = recorderAppContent.includes('ELECTRON_BRIDGE_INTERCEPT');
    console.log('  recorderApp.js intercept:', hasAppIntercept ? '✅' : '❌');
    
    // 检查recorderRunner.js
    const runnerPath = path.join('node_modules', 'playwright-core', 'lib', 'server', 'recorder', 'recorderRunner.js');
    const runnerContent = fs.readFileSync(runnerPath, 'utf-8');
    const hasRunnerPatch = runnerContent.includes('GLOBAL_REPLAY_API_PATCH');
    console.log('  recorderRunner.js patch:', hasRunnerPatch ? '✅' : '❌');
    
    // 4. 模拟完整的代码生成流程
    console.log('🔄 模拟完整代码生成流程:');
    
    // 设置拦截计数器
    let interceptCount = 0;
    let lastInterceptedData = null;
    
    // 包装originalSetSources函数
    const originalFunction = global.playwrightElectronBridge.originalSetSources;
    global.playwrightElectronBridge.originalSetSources = function(sources, primaryPageURL) {
      interceptCount++;
      lastInterceptedData = { sources, primaryPageURL, timestamp: Date.now() };
      console.log(`  📡 拦截 #${interceptCount}: ${sources.length} sources, URL: ${primaryPageURL}`);
      
      // 显示第一个source的详细信息
      if (sources.length > 0) {
        const firstSource = sources[0];
        console.log(`    - 第一个source: ${firstSource.label || firstSource.id}`);
        console.log(`    - 代码长度: ${firstSource.text?.length || 0} 字符`);
        console.log(`    - 是否为录制: ${firstSource.isRecorded}`);
      }
      
      // 调用原始函数
      return originalFunction.call(this, sources, primaryPageURL);
    };
    
    // 模拟多次代码生成
    const testSources = [
      {
        isPrimary: true,
        timestamp: Date.now(),
        isRecorded: true,
        label: 'JavaScript',
        id: 'javascript',
        text: 'const { chromium } = require("playwright");\n\n(async () => {\n  const browser = await chromium.launch();\n  const page = await browser.newPage();\n  await page.goto("https://example.com");\n  await browser.close();\n})();',
        language: 'javascript',
        highlight: []
      },
      {
        isPrimary: false,
        timestamp: Date.now(),
        isRecorded: true,
        label: 'Python',
        id: 'python',
        text: 'from playwright.sync_api import sync_playwright\n\nwith sync_playwright() as p:\n    browser = p.chromium.launch()\n    page = browser.new_page()\n    page.goto("https://example.com")\n    browser.close()',
        language: 'python',
        highlight: []
      }
    ];
    
    // 模拟第一次代码生成
    console.log('  🧪 模拟第一次代码生成...');
    global.playwrightElectronBridge.originalSetSources(testSources, 'https://example.com');
    
    // 模拟第二次代码生成（添加新动作）
    console.log('  🧪 模拟第二次代码生成（添加动作）...');
    const updatedSources = testSources.map(source => ({
      ...source,
      text: source.text + '\n  // 新添加的动作\n  await page.click("#button");',
      timestamp: Date.now()
    }));
    global.playwrightElectronBridge.originalSetSources(updatedSources, 'https://example.com');
    
    // 5. 验证拦截结果
    console.log('📊 拦截结果验证:');
    console.log(`  总拦截次数: ${interceptCount}`);
    console.log(`  最后拦截时间: ${lastInterceptedData ? new Date(lastInterceptedData.timestamp).toLocaleTimeString() : '无'}`);
    console.log(`  最后拦截sources数量: ${lastInterceptedData?.sources?.length || 0}`);
    
    if (interceptCount >= 2) {
      console.log('✅ 代码生成拦截功能正常工作');
    } else {
      console.log('⚠️ 代码生成拦截次数不足');
    }
    
    // 6. 测试IPC事件发送（模拟Electron环境）
    console.log('📨 IPC事件发送测试:');
    
    // 模拟Electron环境
    const originalVersions = process.versions;
    process.versions = { ...originalVersions, electron: '22.0.0' };
    
    // 模拟BrowserWindow
    const mockBrowserWindow = {
      getAllWindows: () => [
        {
          isDestroyed: () => false,
          webContents: {
            send: (event, data) => {
              console.log(`  📤 IPC事件发送: ${event}`, {
                type: data.type,
                sourcesCount: data.sources?.length || 0,
                primaryPageURL: data.primaryPageURL
              });
            }
          }
        }
      ]
    };
    
    // 临时替换require
    const originalRequire = require;
    require = function(moduleName) {
      if (moduleName === 'electron') {
        return { BrowserWindow: mockBrowserWindow };
      }
      return originalRequire.apply(this, arguments);
    };
    
    try {
      // 在模拟Electron环境中测试
      global.playwrightElectronBridge.originalSetSources(testSources, 'https://example.com');
      console.log('✅ IPC事件发送测试成功');
    } catch (error) {
      console.log('⚠️ IPC事件发送测试失败:', error.message);
    } finally {
      // 恢复原始环境
      require = originalRequire;
      process.versions = originalVersions;
    }
    
    console.log('\n🎉 最终代码生成拦截测试完成！');
    
    // 总结
    const allChecks = [
      hasRecorderPatch,
      hasAppIntercept,
      hasRunnerPatch,
      interceptCount >= 2,
      !!global.playwrightElectronBridge?.originalSetSources
    ];
    
    const passedChecks = allChecks.filter(Boolean).length;
    const totalChecks = allChecks.length;
    
    console.log(`\n📋 测试总结: ${passedChecks}/${totalChecks} 项检查通过`);
    
    if (passedChecks === totalChecks) {
      console.log('✅ 所有检查通过，代码生成拦截功能完全正常！');
      console.log('🎯 现在在录制器工具栏中应该能够看到Playwright生成的代码了');
      return true;
    } else {
      console.log('⚠️ 部分检查未通过，可能需要进一步调试');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  finalCodeGenerationTest().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(console.error);
}

module.exports = { finalCodeGenerationTest };
