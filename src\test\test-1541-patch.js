/**
 * 测试1.54.1版本的patch是否正常工作
 */

console.log('🧪 测试 Playwright 1.54.1 patch...');

// 导入必要的模块来触发patch
const path = require('path');

try {
  // 导入 recorder 模块
  console.log('📦 导入 recorder 模块...');
  const recorderPath = path.join(__dirname, '../../node_modules/playwright-core/lib/server/recorder.js');
  require(recorderPath);
  
  // 导入 recorderRunner 模块
  console.log('📦 导入 recorderRunner 模块...');
  const runnerPath = path.join(__dirname, '../../node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
  require(runnerPath);
  
  // 导入 recorderUtils 模块
  console.log('📦 导入 recorderUtils 模块...');
  const utilsPath = path.join(__dirname, '../../node_modules/playwright-core/lib/server/recorder/recorderUtils.js');
  require(utilsPath);
  
  console.log('✅ 所有模块导入成功');
  
} catch (error) {
  console.error('❌ 模块导入失败:', error.message);
  process.exit(1);
}

// 检查全局API是否可用
console.log('\n🔍 检查全局API状态...');

if (global.playwrightReplayAPI) {
  console.log('✅ 全局API可用！');
  console.log('📋 API版本:', global.playwrightReplayAPI.version);
  console.log('📋 Patch版本:', global.playwrightReplayAPI.patchVersion);
  console.log('📋 是否为patch API:', global.playwrightReplayAPI.isPatchedAPI);
  console.log('📋 可用方法:', Object.keys(global.playwrightReplayAPI));
  
  // 测试各个方法是否存在
  const expectedMethods = [
    'performAction',
    'performActionOriginal',
    'toClickOptions',
    'buildFullSelector',
    'mainFrameForAction',
    'frameForAction',
    'createActionInContext',
    'executeActionSequence'
  ];
  
  console.log('\n🔬 验证API方法...');
  let allMethodsPresent = true;
  
  for (const method of expectedMethods) {
    if (typeof global.playwrightReplayAPI[method] !== 'undefined') {
      console.log(`✅ ${method}: ${typeof global.playwrightReplayAPI[method]}`);
    } else {
      console.log(`❌ ${method}: 缺失`);
      allMethodsPresent = false;
    }
  }
  
  if (allMethodsPresent) {
    console.log('\n🎉 所有API方法都正确暴露！');
    
    // 测试 createActionInContext 函数
    try {
      const actionInContext = global.playwrightReplayAPI.createActionInContext('page', [], {
        name: 'navigate',
        url: 'https://www.baidu.com'
      });
      
      console.log('\n📊 createActionInContext 测试:');
      console.log('  类型:', typeof actionInContext);
      console.log('  frame.pageAlias:', actionInContext.frame.pageAlias);
      console.log('  action.name:', actionInContext.action.name);
      console.log('  action.url:', actionInContext.action.url);
      console.log('✅ createActionInContext 函数正常工作');
    } catch (error) {
      console.error('❌ createActionInContext 测试失败:', error.message);
    }
    
  } else {
    console.log('\n❌ 部分API方法缺失');
  }
  
} else {
  console.log('❌ 全局API不可用');
  console.log('💡 可能的原因:');
  console.log('  1. patch未正确应用');
  console.log('  2. 模块未正确导入');
  console.log('  3. 模块导入时发生错误');
}

// 检查 Electron 集成
console.log('\n🔍 检查 Electron 集成...');
if (global.playwrightElectronBridge) {
  console.log('✅ Electron 集成可用');
  console.log('📋 可用方法:', Object.keys(global.playwrightElectronBridge));
} else {
  console.log('⚠️ Electron 集成不可用（这是正常的，因为没有设置环境变量）');
}

console.log('\n🏁 测试完成');
console.log('\n💡 使用方法:');
console.log('  1. 设置环境变量 PLAYWRIGHT_ELECTRON_BRIDGE=true 启用 Electron 集成');
console.log('  2. 使用 global.playwrightReplayAPI.performAction() 执行动作');
console.log('  3. 使用 global.playwrightReplayAPI.createActionInContext() 创建动作上下文');
