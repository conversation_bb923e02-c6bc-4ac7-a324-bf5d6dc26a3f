{"name": "@playwright/mdd", "version": "0.0.1", "description": "Playwright <PERSON><PERSON>", "private": true, "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "dependencies": {"commander": "^13.1.0", "debug": "^4.4.1", "dotenv": "^16.5.0", "mime": "^4.0.7", "openai": "^5.7.0", "playwright-core": "1.54.1", "zod-to-json-schema": "^3.24.4"}, "devDependencies": {"@types/debug": "^4.1.7"}, "bin": {"playwright-mdd": "cli.js"}}