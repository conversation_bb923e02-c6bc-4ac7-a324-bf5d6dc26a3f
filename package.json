{"name": "playwright-electron-recorder", "version": "1.0.0", "description": "Electron + Playwright 网页自动化测试录制客户端", "main": "src/main/main.js", "homepage": "http://./", "scripts": {"postinstall": "patch-package", "start": "cross-env PLAYWRIGHT_ELECTRON_BRIDGE=true node start.js", "start:renderer": "vite", "start:electron": "npx electron .", "build": "vite build", "build:electron": "npm run build && electron-builder", "dev": "npm run start", "test": "playwright test", "start:legacy": "concurrently \"npm run start:renderer\" \"wait-on http://localhost:3000 && npm run start:electron\""}, "keywords": ["electron", "playwright", "automation", "testing", "recorder"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.0.0", "electron": "^28.0.0", "electron-builder": "^24.0.0", "vite": "^5.0.0", "wait-on": "^7.0.0"}, "dependencies": {"cross-env": "^7.0.3", "patch-package": "^8.0.0", "playwright": "^1.54.1", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0"}, "build": {"appId": "com.playwright.recorder", "productName": "Playwright Recorder", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main/**/*", "node_modules/**/*"]}}